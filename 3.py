#!/usr/bin/env python
# -*- coding: utf-8 -*-
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
file_name = r'E:\AFM课题组\算法\LSTM\LSTM-KF-main\LSTM-KF-main\lstm_kF\2D\flight_tt.xls'
flight_data  = pd.read_excel(file_name)
flight_data.shape #(144, 3)

fig_size = plt.rcParams["figure.figsize"]
fig_size[0] = 15
fig_size[1] = 5
plt.rcParams["figure.figsize"] = fig_size

x_data = flight_data['x'].values.astype(float)
y_data = flight_data['y'].values.astype(float)

all_data = np.zeros((len(x_data),2))
all_data[:,0] = x_data
all_data[:,1] = y_data

test_data_size = 12
train_data = all_data[:-test_data_size]
test_data = all_data[-test_data_size:]

scaler = MinMaxScaler(feature_range=(-1, 1))
train_data_normalized = scaler.fit_transform(train_data .reshape(-1, 2))

train_data_normalized = torch.FloatTensor(train_data_normalized)

def create_inout_sequences(input_data, tw):
    inout_seq = []
    L = len(input_data)
    for i in range(L-tw):
        train_seq = input_data[i:i+tw]
        train_label = input_data[i+tw:i+tw+1]
        inout_seq.append((train_seq ,train_label))
    return inout_seq

train_window = 20
train_inout_seq = create_inout_sequences(train_data_normalized, train_window)

class LSTM(nn.Module):
    def __init__(self, input_size=2, hidden_size=100, output_size=2):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size,num_layers = 1)
        self.linear = nn.Linear(hidden_size, output_size)

    def forward(self, input_seq):
        lstm_out, self.hidden_cell = self.lstm(input_seq.view(len(input_seq) ,1, -1), self.hidden_cell)
        predictions = self.linear(lstm_out.view(len(input_seq), -1))
        return predictions[-1]

model_path = r'lstm_model_total.pt'
model = torch.load(model_path)

fut_pred = 1
test_inputs = train_data_normalized[-train_window:].tolist()

model.eval()

font = {'family': 'SimSun',  # 宋体
        'size': '10.5'  # 五号
        }
plt.rc('font', **font)
plt.rc('axes', unicode_minus=False)

class Kf_Params:
    B = 0  # 外部输入为0
    u = 0  # 外部输入为0
    K = float('nan')  # 卡尔曼增益无需初始化
    z = float('nan')  # 这里无需初始化，每次使用kf_update之前需要输入观察值z
    P = np.diag(np.ones(4))  # 初始P设为0 ??? zeros(4, 4)
    x = []
    G = []
    A = np.eye(4) + np.diag(np.ones((1, 2))[0, :], 2)
    Q = np.diag(np.ones(4)) * 0.1
    H = np.eye(2, 4)
    R = np.diag(np.ones(2)) * 0.1

def kf_init(px, py, vx, vy):
    kf_params = Kf_Params()
    kf_params.B = 0
    kf_params.u = 0
    kf_params.K = float('nan')
    kf_params.z = float('nan')
    kf_params.P = np.diag(np.ones(4))
    kf_params.x = [px, py, vx, vy]
    kf_params.G = [px, py, vx, vy]
    kf_params.A = np.eye(4) + np.diag(np.ones((1, 2))[0, :], 2)
    kf_params.Q = np.diag(np.ones(4)) * 0.1
    kf_params.H = np.eye(2, 4)
    kf_params.R = np.diag(np.ones(2)) * 0.1
    return kf_params

def kf_update(kf_params,t):
    a1 = np.dot(kf_params.A, kf_params.x)
    a2 = kf_params.B * kf_params.u
    x_ = np.array(a1) + np.array(a2)
    
    for i in range(fut_pred):
        seq = torch.FloatTensor(test_inputs[-train_window:])
        with torch.no_grad():
            model.hidden = (torch.zeros(1, 1, model.hidden_size),
                            torch.zeros(1, 1, model.hidden_size))
            list_model_seq = [model(seq)[0].item(),model(seq)[1].item()]
            test_inputs.append(list_model_seq)
    actual_predictions = scaler.inverse_transform(np.array(test_inputs[train_window:] ).reshape(-1, 2))
    print(actual_predictions)
    print(t)

    x_[0] = actual_predictions[t-1][0]
    x_[1] = actual_predictions[t-1][1]
    print(x_)

    b1 = np.dot(kf_params.A, kf_params.P)
    b2 = np.dot(b1, np.transpose(kf_params.A))
    p_ = np.array(b2) + np.array(kf_params.Q)
 
    c1 = np.dot(p_, np.transpose(kf_params.H))
    c2 = np.dot(kf_params.H, p_)
    c3 = np.dot(c2, np.transpose(kf_params.H))
    c4 = np.array(c3) + np.array(kf_params.R)
    c5 = np.linalg.matrix_power(c4, -1)
    kf_params.K = np.dot(c1, c5)
 
    d1 = np.dot(kf_params.H, x_)
    d2 = np.array(kf_params.z) - np.array(d1)
    d3 = np.dot(kf_params.K, d2)
    kf_params.x = np.array(x_) + np.array(d3)
 
    e1 = np.dot(kf_params.K, kf_params.H)
    e2 = np.dot(e1, p_)
    kf_params.P = np.array(p_) - np.array(e2)
 
    kf_params.G = x_
    return kf_params

def accuracy(predictions, labels):
    return np.array(predictions) - np.array(labels)

def lstm_only_predict(data_x, data_y, num_predictions):
    """仅使用LSTM进行预测"""
    # 准备输入数据
    all_data_lstm = np.zeros((len(data_x), 2))
    all_data_lstm[:, 0] = data_x
    all_data_lstm[:, 1] = data_y

    # 数据归一化
    scaler_lstm = MinMaxScaler(feature_range=(-1, 1))
    normalized_data = scaler_lstm.fit_transform(all_data_lstm)

    # 使用最后train_window个数据点作为初始输入
    lstm_inputs = normalized_data[-train_window:].tolist()

    # 进行预测
    predictions = []
    model.eval()

    for i in range(num_predictions):
        seq = torch.FloatTensor(lstm_inputs[-train_window:])
        with torch.no_grad():
            model.hidden = (torch.zeros(1, 1, model.hidden_size),
                           torch.zeros(1, 1, model.hidden_size))
            pred = model(seq)
            pred_list = [pred[0].item(), pred[1].item()]
            lstm_inputs.append(pred_list)
            predictions.append(pred_list)

    # 反归一化
    predictions = np.array(predictions)
    predictions_denorm = scaler_lstm.inverse_transform(predictions)

    return predictions_denorm

def moving_average_smooth(lstm_predictions, window_size=3):
    """移动平均滤波优化LSTM预测结果"""
    smoothed = np.copy(lstm_predictions)
    for i in range(window_size, len(lstm_predictions)):
        smoothed[i] = np.mean(lstm_predictions[i-window_size:i+1], axis=0)
    return smoothed

def exponential_smooth(lstm_predictions, alpha=0.3):
    """指数平滑优化LSTM预测结果"""
    smoothed = np.copy(lstm_predictions)
    for i in range(1, len(lstm_predictions)):
        smoothed[i] = alpha * lstm_predictions[i] + (1 - alpha) * smoothed[i-1]
    return smoothed

def weighted_average_smooth(lstm_predictions, weights=None):
    """加权平均滤波优化LSTM预测结果"""
    if weights is None:
        weights = [0.1, 0.2, 0.4, 0.2, 0.1]  # 中心权重最大

    window_size = len(weights)
    smoothed = np.copy(lstm_predictions)

    for i in range(window_size//2, len(lstm_predictions) - window_size//2):
        weighted_sum = np.zeros(2)
        for j, weight in enumerate(weights):
            idx = i - window_size//2 + j
            weighted_sum += weight * lstm_predictions[idx]
        smoothed[i] = weighted_sum

    return smoothed

if __name__ == '__main__':
    plt.figure(figsize=(12, 8))

    path = r'true.csv'
    data_B = pd.read_csv(path)
    data_B_x = list(data_B.iloc[::, 0])
    data_B_y = list(data_B.iloc[::, 1])
    B = np.array(list(zip(data_B_x, data_B_y)))

    x_tick = np.arange(0, 12, 1)

    # 绘制观测值
    plt.plot(x_tick, data_B_x, color="red", linestyle='-', linewidth=2, label='观测的x中心值')

    # 卡尔曼滤波 + LSTM 预测
    kf_params_record = np.zeros((len(data_B), 4))
    kf_params_p = np.zeros((len(data_B), 4))
    t = len(data_B)

    kalman_filter_params = kf_init(data_B_x[0], data_B_y[0], 0, 0)
    for i in range(t):
        if i == 0:
            kalman_filter_params = kf_init(data_B_x[i], data_B_y[i], 0, 0)  # 初始化
        else:
            kalman_filter_params.z = np.transpose([data_B_x[i], data_B_y[i]])  # 设置当前时刻的观测位置
            kalman_filter_params = kf_update(kalman_filter_params, i)  # 卡尔曼滤波

        kf_params_record[i, ::] = np.transpose(kalman_filter_params.x)
        kf_params_p[i, ::] = np.transpose(kalman_filter_params.G)

    kf_trace = kf_params_record[::, :2]

    # 绘制卡尔曼滤波+LSTM结果
    plt.plot(x_tick, kf_trace[::, 0], color="blue", linestyle='--', linewidth=2, label='卡尔曼滤波+LSTM的x值')

    # 仅使用LSTM进行预测
    lstm_predictions = lstm_only_predict(data_B_x, data_B_y, len(data_B))
    lstm_x_predictions = lstm_predictions[:, 0]

    # 应用不同的优化方法
    ma_predictions = moving_average_smooth(lstm_predictions, window_size=3)
    exp_predictions = exponential_smooth(lstm_predictions, alpha=0.3)
    wa_predictions = weighted_average_smooth(lstm_predictions)

    # 绘制不同方法的结果
    plt.plot(x_tick, lstm_x_predictions, color="green", linestyle=':', linewidth=2, label='仅LSTM')
    plt.plot(x_tick, ma_predictions[:, 0], color="orange", linestyle='-.', linewidth=2, label='LSTM+移动平均')
    plt.plot(x_tick, exp_predictions[:, 0], color="purple", linestyle='--', linewidth=2, label='LSTM+指数平滑')
    plt.plot(x_tick, wa_predictions[:, 0], color="brown", linestyle='-', linewidth=2, label='LSTM+加权平均')

    plt.legend(loc="best", frameon=False)
    plt.title('卡尔曼滤波+LSTM vs 其他优化方法预测效果比较', fontsize=14)
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('x坐标值', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.savefig('comparison_result.svg', dpi=600, bbox_inches='tight')
    plt.show()
    print("预测结果比较图已保存为 comparison_result.svg")

    # 创建误差对比图
    plt.figure(figsize=(15, 10))

    # 计算所有方法的误差
    kf_lstm_error_x = kf_trace[:, 0] - data_B_x
    lstm_only_error_x = lstm_x_predictions - data_B_x
    ma_error_x = ma_predictions[:, 0] - data_B_x
    exp_error_x = exp_predictions[:, 0] - data_B_x
    wa_error_x = wa_predictions[:, 0] - data_B_x

    # 绘制误差曲线对比
    plt.subplot(2, 1, 1)
    plt.plot(x_tick, kf_lstm_error_x, color="blue", linestyle='-', linewidth=2, marker='o', label='卡尔曼滤波+LSTM')
    plt.plot(x_tick, lstm_only_error_x, color="green", linestyle=':', linewidth=2, marker='s', label='仅LSTM')
    plt.plot(x_tick, ma_error_x, color="orange", linestyle='-.', linewidth=2, marker='^', label='LSTM+移动平均')
    plt.plot(x_tick, exp_error_x, color="purple", linestyle='--', linewidth=2, marker='d', label='LSTM+指数平滑')
    plt.plot(x_tick, wa_error_x, color="brown", linestyle='-', linewidth=2, marker='*', label='LSTM+加权平均')
    plt.axhline(y=0, color='red', linestyle=':', alpha=0.7, label='零误差线')
    plt.legend(loc="best", frameon=False)
    plt.title('x方向预测误差对比 - 所有优化方法', fontsize=14)
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('误差值', fontsize=12)
    plt.grid(True, alpha=0.3)

    # 绘制绝对误差对比
    plt.subplot(2, 1, 2)
    plt.plot(x_tick, np.abs(kf_lstm_error_x), color="blue", linestyle='-', linewidth=2, marker='o', label='卡尔曼滤波+LSTM')
    plt.plot(x_tick, np.abs(lstm_only_error_x), color="green", linestyle=':', linewidth=2, marker='s', label='仅LSTM')
    plt.plot(x_tick, np.abs(ma_error_x), color="orange", linestyle='-.', linewidth=2, marker='^', label='LSTM+移动平均')
    plt.plot(x_tick, np.abs(exp_error_x), color="purple", linestyle='--', linewidth=2, marker='d', label='LSTM+指数平滑')
    plt.plot(x_tick, np.abs(wa_error_x), color="brown", linestyle='-', linewidth=2, marker='*', label='LSTM+加权平均')
    plt.legend(loc="best", frameon=False)
    plt.title('x方向预测绝对误差对比 - 所有优化方法', fontsize=14)
    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('绝对误差值', fontsize=12)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('error_comparison.svg', dpi=600, bbox_inches='tight')
    plt.show()
    print("误差对比图已保存为 error_comparison.svg")

    # 计算所有方法的精度比较
    kf_lstm_accuracy = accuracy(kf_trace, B)
    lstm_only_accuracy = accuracy(lstm_predictions, B)
    ma_accuracy = accuracy(ma_predictions, B)
    exp_accuracy = accuracy(exp_predictions, B)
    wa_accuracy = accuracy(wa_predictions, B)

    # 计算所有方法的RMSE
    kf_lstm_rmse_x = np.sqrt(np.mean(kf_lstm_accuracy[:, 0]**2))
    lstm_only_rmse_x = np.sqrt(np.mean(lstm_only_accuracy[:, 0]**2))
    ma_rmse_x = np.sqrt(np.mean(ma_accuracy[:, 0]**2))
    exp_rmse_x = np.sqrt(np.mean(exp_accuracy[:, 0]**2))
    wa_rmse_x = np.sqrt(np.mean(wa_accuracy[:, 0]**2))

    print("\n" + "="*60)
    print("各种优化方法的x方向RMSE比较:")
    print("="*60)
    print(f"1. 卡尔曼滤波+LSTM:  {kf_lstm_rmse_x:.4f}")
    print(f"2. 仅LSTM:          {lstm_only_rmse_x:.4f}")
    print(f"3. LSTM+移动平均:    {ma_rmse_x:.4f}")
    print(f"4. LSTM+指数平滑:    {exp_rmse_x:.4f}")
    print(f"5. LSTM+加权平均:    {wa_rmse_x:.4f}")

    print("\n" + "="*60)
    print("相对于仅LSTM的改进程度:")
    print("="*60)
    print(f"卡尔曼滤波+LSTM:  {((lstm_only_rmse_x - kf_lstm_rmse_x) / lstm_only_rmse_x * 100):.2f}%")
    print(f"LSTM+移动平均:    {((lstm_only_rmse_x - ma_rmse_x) / lstm_only_rmse_x * 100):.2f}%")
    print(f"LSTM+指数平滑:    {((lstm_only_rmse_x - exp_rmse_x) / lstm_only_rmse_x * 100):.2f}%")
    print(f"LSTM+加权平均:    {((lstm_only_rmse_x - wa_rmse_x) / lstm_only_rmse_x * 100):.2f}%")

    # 找出最佳方法
    rmse_values = [kf_lstm_rmse_x, lstm_only_rmse_x, ma_rmse_x, exp_rmse_x, wa_rmse_x]
    method_names = ['卡尔曼滤波+LSTM', '仅LSTM', 'LSTM+移动平均', 'LSTM+指数平滑', 'LSTM+加权平均']
    best_idx = np.argmin(rmse_values)

    print(f"\n🏆 最佳方法: {method_names[best_idx]} (RMSE: {rmse_values[best_idx]:.4f})")
    print("="*60)
