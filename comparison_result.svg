<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="727.109375pt" height="506.045pt" viewBox="0 0 727.109375 506.045" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-06-12T17:49:42.455379</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 506.045 
L 727.109375 506.045 
L 727.109375 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 50.309375 467.87625 
L 719.909375 467.87625 
L 719.909375 24.35625 
L 50.309375 24.35625 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 80.745739 467.87625 
L 80.745739 24.35625 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m7cf30947d5" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m7cf30947d5" x="80.745739" y="467.87625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g transform="translate(78.120739 482.382109)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-30" d="M 1600 4225 
Q 1250 4225 1012 3687 
Q 775 3150 775 2250 
Q 775 1300 1012 775 
Q 1250 250 1600 250 
Q 1975 250 2187 775 
Q 2400 1300 2400 2250 
Q 2400 3150 2200 3687 
Q 2000 4225 1600 4225 
z
M 1600 50 
Q 1050 50 675 625 
Q 300 1200 300 2250 
Q 300 3225 662 3825 
Q 1025 4425 1600 4425 
Q 2150 4425 2512 3850 
Q 2875 3275 2875 2250 
Q 2875 1225 2512 637 
Q 2150 50 1600 50 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 191.423425 467.87625 
L 191.423425 24.35625 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m7cf30947d5" x="191.423425" y="467.87625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g transform="translate(188.798425 482.382109)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-32" d="M 2325 3325 
Q 2325 3775 2125 4012 
Q 1925 4250 1525 4250 
Q 1225 4250 1012 4087 
Q 800 3925 800 3675 
Q 800 3525 900 3425 
Q 975 3325 975 3225 
Q 975 3100 912 3037 
Q 850 2975 725 2975 
Q 575 2975 487 3062 
Q 400 3150 400 3350 
Q 400 3875 775 4150 
Q 1150 4425 1575 4425 
Q 2175 4425 2462 4125 
Q 2750 3825 2750 3375 
Q 2750 3075 2612 2775 
Q 2475 2475 2175 2200 
Q 1450 1500 1062 1062 
Q 675 625 600 475 
L 2075 475 
Q 2300 475 2450 650 
Q 2600 825 2650 1175 
L 2800 1175 
L 2650 100 
L 325 100 
L 325 425 
Q 450 650 737 1000 
Q 1025 1350 1500 1825 
Q 1925 2250 2125 2625 
Q 2325 3000 2325 3325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 302.101111 467.87625 
L 302.101111 24.35625 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m7cf30947d5" x="302.101111" y="467.87625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 4 -->
      <g transform="translate(299.476111 482.382109)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-34" d="M 2300 575 
Q 2300 400 2400 325 
Q 2500 250 2675 250 
L 2900 250 
L 2900 100 
L 1250 100 
L 1250 250 
L 1525 250 
Q 1725 250 1812 325 
Q 1900 400 1900 575 
L 1900 1400 
L 225 1400 
L 225 1525 
L 2050 4425 
L 2300 4425 
L 2300 1550 
L 2975 1550 
L 2975 1400 
L 2300 1400 
L 2300 575 
z
M 1875 3800 
L 450 1550 
L 1900 1550 
L 1900 3800 
L 1875 3800 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 412.778796 467.87625 
L 412.778796 24.35625 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m7cf30947d5" x="412.778796" y="467.87625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 6 -->
      <g transform="translate(410.153796 482.382109)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-36" d="M 775 1800 
Q 775 1050 1037 637 
Q 1300 225 1675 225 
Q 2050 225 2250 500 
Q 2450 775 2450 1525 
Q 2450 2025 2237 2300 
Q 2025 2575 1725 2575 
Q 1450 2575 1212 2412 
Q 975 2250 775 1800 
z
M 1750 2825 
Q 2350 2825 2612 2425 
Q 2875 2025 2875 1525 
Q 2875 775 2512 412 
Q 2150 50 1675 50 
Q 1000 50 650 562 
Q 300 1075 300 2050 
Q 300 3200 725 3812 
Q 1150 4425 1875 4425 
Q 2225 4425 2437 4225 
Q 2650 4025 2650 3875 
Q 2650 3725 2587 3650 
Q 2525 3575 2375 3575 
Q 2250 3575 2187 3637 
Q 2125 3700 2125 3825 
Q 2125 3875 2150 3925 
Q 2150 3975 2150 4025 
Q 2150 4125 2075 4187 
Q 2000 4250 1800 4250 
Q 1375 4250 1075 3862 
Q 775 3475 775 2175 
Q 925 2500 1187 2662 
Q 1450 2825 1750 2825 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 523.456482 467.87625 
L 523.456482 24.35625 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m7cf30947d5" x="523.456482" y="467.87625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 8 -->
      <g transform="translate(520.831482 482.382109)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-38" d="M 625 1125 
Q 625 725 887 475 
Q 1150 225 1550 225 
Q 2025 225 2237 475 
Q 2450 725 2450 1100 
Q 2450 1425 2150 1725 
Q 1850 2025 1250 2275 
Q 950 2075 787 1775 
Q 625 1475 625 1125 
z
M 2825 1175 
Q 2825 700 2475 375 
Q 2125 50 1550 50 
Q 1025 50 650 375 
Q 275 700 275 1125 
Q 275 1550 487 1850 
Q 700 2150 1100 2375 
Q 750 2550 562 2812 
Q 375 3075 375 3400 
Q 375 3825 700 4125 
Q 1025 4425 1600 4425 
Q 2125 4425 2450 4125 
Q 2775 3825 2775 3400 
Q 2775 3100 2587 2837 
Q 2400 2575 2000 2375 
Q 2425 2125 2625 1825 
Q 2825 1525 2825 1175 
z
M 2425 3375 
Q 2425 3725 2225 3987 
Q 2025 4250 1600 4250 
Q 1125 4250 925 4000 
Q 725 3750 725 3475 
Q 725 3200 1012 2937 
Q 1300 2675 1825 2475 
Q 2125 2625 2275 2850 
Q 2425 3075 2425 3375 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-38"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 634.134168 467.87625 
L 634.134168 24.35625 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m7cf30947d5" x="634.134168" y="467.87625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 10 -->
      <g transform="translate(628.884168 482.382109)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-31" d="M 1825 4450 
L 1825 650 
Q 1825 450 1950 350 
Q 2075 250 2300 250 
L 2550 250 
L 2550 100 
L 725 100 
L 725 250 
L 950 250 
Q 1200 250 1312 350 
Q 1425 450 1425 650 
L 1425 3675 
Q 1425 3775 1362 3837 
Q 1300 3900 1175 3900 
L 725 3900 
L 725 4050 
L 950 4050 
Q 1250 4050 1437 4150 
Q 1625 4250 1725 4450 
L 1825 4450 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="text_7">
     <!-- 时间步 -->
     <g transform="translate(367.109375 497.298125)scale(0.12 -0.12)">
      <defs>
       <path id="SimSun-65f6" d="M 4500 3600 
Q 4500 4575 4475 5225 
L 5100 4900 
L 4850 4725 
L 4850 3600 
L 5225 3600 
L 5550 3925 
L 6025 3450 
L 4850 3450 
L 4850 100 
Q 4850 -375 4300 -575 
Q 4275 -200 3475 -25 
L 3475 100 
Q 4150 0 4325 25 
Q 4500 50 4500 325 
L 4500 3450 
L 3200 3450 
Q 2875 3450 2600 3375 
L 2375 3600 
L 4500 3600 
z
M 2875 2825 
Q 3525 2375 3637 2200 
Q 3750 2025 3750 1925 
Q 3750 1800 3637 1637 
Q 3525 1475 3475 1475 
Q 3400 1475 3325 1775 
Q 3200 2225 2800 2750 
L 2875 2825 
z
M 925 4150 
L 925 2650 
L 1950 2650 
L 1950 4150 
L 925 4150 
z
M 925 2500 
L 925 900 
L 1950 900 
L 1950 2500 
L 925 2500 
z
M 2300 2000 
Q 2300 800 2325 450 
L 1950 250 
L 1950 750 
L 925 750 
L 925 225 
L 550 25 
Q 575 825 575 2425 
Q 575 4025 550 4500 
L 950 4300 
L 1900 4300 
L 2100 4550 
L 2500 4225 
L 2300 4050 
L 2300 2000 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-95f4" d="M 2150 525 
Q 2175 1325 2175 2112 
Q 2175 2900 2150 3675 
L 2525 3450 
L 3750 3450 
L 4000 3700 
L 4325 3375 
L 4125 3200 
Q 4125 1550 4150 775 
L 3800 575 
L 3800 975 
L 2500 975 
L 2500 700 
L 2150 525 
z
M 2500 3300 
L 2500 2325 
L 3800 2325 
L 3800 3300 
L 2500 3300 
z
M 2500 2175 
L 2500 1125 
L 3800 1125 
L 3800 2175 
L 2500 2175 
z
M 1275 5200 
Q 1850 4825 1950 4675 
Q 2050 4525 2050 4425 
Q 2050 4275 1950 4137 
Q 1850 4000 1825 4000 
Q 1750 4000 1675 4275 
Q 1550 4650 1200 5125 
L 1275 5200 
z
M 1125 425 
Q 1125 25 1150 -375 
L 750 -525 
Q 775 -150 775 1950 
Q 775 4050 750 4425 
L 1350 4125 
L 1125 3975 
L 1125 425 
z
M 4225 125 
Q 5025 0 5125 37 
Q 5225 75 5225 250 
L 5225 4450 
L 3225 4450 
Q 2900 4450 2625 4375 
L 2400 4600 
L 5175 4600 
L 5425 4850 
L 5775 4500 
L 5575 4350 
L 5575 225 
Q 5575 -100 5462 -250 
Q 5350 -400 5000 -550 
Q 4800 -125 4225 25 
L 4225 125 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6b65" d="M 3125 2975 
Q 3125 4700 3100 5275 
L 3700 5000 
L 3475 4825 
L 3475 4200 
L 4800 4200 
L 5100 4500 
L 5550 4050 
L 3475 4050 
L 3475 2975 
L 5175 2975 
L 5550 3350 
L 6075 2825 
L 1150 2825 
Q 825 2825 550 2750 
L 325 2975 
L 1500 2975 
Q 1500 4225 1475 4650 
L 2075 4375 
L 1850 4200 
L 1850 2975 
L 3125 2975 
z
M 3100 650 
Q 3125 950 3125 1737 
Q 3125 2525 3100 2775 
L 3675 2500 
L 3475 2325 
Q 3475 1175 3500 800 
L 3100 650 
z
M 1850 2300 
L 2350 1925 
L 2075 1825 
Q 1275 750 550 250 
L 500 325 
Q 975 800 1350 1387 
Q 1725 1975 1850 2300 
z
M 5250 2275 
L 5700 1825 
L 5475 1775 
Q 3950 325 2875 -62 
Q 1800 -450 375 -650 
L 375 -550 
Q 1125 -425 2075 -112 
Q 3025 200 3862 837 
Q 4700 1475 5250 2275 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-65f6"/>
      <use xlink:href="#SimSun-95f4" x="100"/>
      <use xlink:href="#SimSun-6b65" x="200"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_13">
      <path d="M 50.309375 424.718439 
L 719.909375 424.718439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_14">
      <defs>
       <path id="m0621111d16" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="424.718439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 0.52 -->
      <g transform="translate(22.309375 428.471368)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-2e" d="M 800 25 
Q 650 25 537 125 
Q 425 225 425 400 
Q 425 575 537 675 
Q 650 775 800 775 
Q 950 775 1062 662 
Q 1175 550 1175 400 
Q 1175 225 1062 125 
Q 950 25 800 25 
z
" transform="scale(0.015625)"/>
        <path id="SimSun-35" d="M 1725 2850 
Q 2200 2850 2500 2487 
Q 2800 2125 2800 1525 
Q 2800 850 2487 450 
Q 2175 50 1525 50 
Q 1075 50 725 312 
Q 375 575 375 950 
Q 375 1100 462 1212 
Q 550 1325 700 1325 
Q 850 1325 900 1225 
Q 950 1125 950 1050 
Q 950 900 875 825 
Q 800 725 800 625 
Q 800 425 1037 325 
Q 1275 225 1550 225 
Q 1950 225 2162 550 
Q 2375 875 2375 1475 
Q 2375 1975 2187 2287 
Q 2000 2600 1625 2600 
Q 1350 2600 1150 2500 
Q 950 2400 775 2075 
L 550 2100 
L 675 4375 
L 2725 4375 
L 2650 4000 
L 850 4000 
L 750 2350 
Q 1000 2700 1237 2775 
Q 1475 2850 1725 2850 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-35" x="100"/>
       <use xlink:href="#SimSun-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_15">
      <path d="M 50.309375 367.374439 
L 719.909375 367.374439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="367.374439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 0.54 -->
      <g transform="translate(22.309375 371.127368)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-35" x="100"/>
       <use xlink:href="#SimSun-34" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_17">
      <path d="M 50.309375 310.030439 
L 719.909375 310.030439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="310.030439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 0.56 -->
      <g transform="translate(22.309375 313.783368)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-35" x="100"/>
       <use xlink:href="#SimSun-36" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_19">
      <path d="M 50.309375 252.686439 
L 719.909375 252.686439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="252.686439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.58 -->
      <g transform="translate(22.309375 256.439368)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-35" x="100"/>
       <use xlink:href="#SimSun-38" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_21">
      <path d="M 50.309375 195.342439 
L 719.909375 195.342439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="195.342439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.60 -->
      <g transform="translate(22.309375 199.095368)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-36" x="100"/>
       <use xlink:href="#SimSun-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_23">
      <path d="M 50.309375 137.998439 
L 719.909375 137.998439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="137.998439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.62 -->
      <g transform="translate(22.309375 141.751368)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-36" x="100"/>
       <use xlink:href="#SimSun-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_25">
      <path d="M 50.309375 80.654439 
L 719.909375 80.654439 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m0621111d16" x="50.309375" y="80.654439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0.64 -->
      <g transform="translate(22.309375 84.407368)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-36" x="100"/>
       <use xlink:href="#SimSun-34" x="150"/>
      </g>
     </g>
    </g>
    <g id="text_15">
     <!-- x坐标值 -->
     <g transform="translate(16.7625 267.11625)rotate(-90)scale(0.12 -0.12)">
      <defs>
       <path id="SimSun-78" d="M 1675 1825 
L 2125 2500 
Q 2200 2600 2187 2675 
Q 2175 2750 2000 2750 
L 1850 2750 
L 1850 2900 
L 2825 2900 
L 2825 2750 
L 2700 2750 
Q 2650 2750 2575 2737 
Q 2500 2725 2450 2650 
L 1775 1650 
L 2475 500 
Q 2550 375 2662 312 
Q 2775 250 2825 250 
L 2925 250 
L 2925 100 
L 1825 100 
L 1825 250 
L 1975 250 
Q 2125 250 2137 325 
Q 2150 400 2100 500 
L 1575 1350 
L 975 475 
Q 950 350 987 300 
Q 1025 250 1125 250 
L 1200 250 
L 1200 100 
L 225 100 
L 225 250 
L 375 250 
Q 475 250 600 325 
Q 725 400 800 550 
L 1450 1525 
L 800 2575 
Q 775 2625 700 2687 
Q 625 2750 500 2750 
L 375 2750 
L 375 2900 
L 1450 2900 
L 1450 2750 
L 1375 2750 
Q 1250 2750 1225 2675 
Q 1200 2600 1250 2500 
L 1675 1825 
z
M 600 250 
L 600 250 
z
M 825 250 
L 825 250 
z
M 2525 2750 
L 2525 2750 
z
M 2300 2750 
L 2300 2750 
z
M 2250 250 
L 2250 250 
z
M 2625 250 
L 2625 250 
z
M 1100 2750 
L 1100 2750 
z
M 700 2750 
L 700 2750 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5750" d="M 1575 4575 
L 2175 4250 
L 1950 4150 
Q 1800 3775 1625 3325 
Q 2100 3075 2325 2875 
Q 2550 2675 2550 2475 
Q 2550 2375 2462 2187 
Q 2375 2000 2325 2000 
Q 2250 2000 2175 2275 
Q 2025 2675 1575 3200 
Q 1025 2175 400 1625 
L 325 1675 
Q 900 2450 1187 3225 
Q 1475 4000 1575 4575 
z
M 4775 4600 
L 5350 4275 
Q 5200 4200 5125 4075 
Q 5050 3950 4825 3425 
Q 5375 3050 5587 2850 
Q 5800 2650 5800 2450 
Q 5800 2325 5725 2162 
Q 5650 2000 5600 2000 
Q 5525 2000 5425 2300 
Q 5250 2750 4775 3325 
Q 4275 2350 3600 1750 
L 3525 1800 
Q 4000 2400 4312 3150 
Q 4625 3900 4775 4600 
z
M 3025 4400 
Q 3025 4750 3000 5200 
L 3600 4900 
L 3375 4725 
L 3375 1475 
L 4550 1475 
L 4925 1850 
L 5450 1325 
L 3375 1325 
L 3375 -75 
L 5125 -75 
L 5550 350 
L 6125 -225 
L 1100 -225 
Q 775 -225 500 -300 
L 275 -75 
L 3025 -75 
L 3025 1325 
L 1875 1325 
Q 1550 1325 1275 1250 
L 1050 1475 
L 3025 1475 
L 3025 4400 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6807" d="M 4775 2225 
Q 5725 1275 5825 1050 
Q 5925 825 5925 700 
Q 5925 525 5825 375 
Q 5725 225 5700 225 
Q 5575 225 5475 650 
Q 5300 1225 4700 2175 
L 4775 2225 
z
M 2750 4600 
L 4975 4600 
L 5275 4900 
L 5725 4450 
L 3175 4450 
L 2950 4400 
L 2750 4600 
z
M 3100 75 
Q 3775 -50 3900 -25 
Q 4025 0 4025 175 
L 4025 2950 
L 2800 2950 
L 2575 2900 
L 2375 3100 
L 5350 3100 
L 5675 3425 
L 6150 2950 
L 4400 2950 
L 4400 -25 
Q 4375 -425 3850 -600 
Q 3850 -300 3100 -50 
L 3100 75 
z
M 3125 2275 
L 3675 1950 
L 3425 1825 
Q 2800 700 2100 25 
L 2025 100 
Q 2725 1025 3125 2275 
z
M 1675 2725 
Q 1675 25 1700 -475 
L 1300 -625 
Q 1325 450 1325 2325 
Q 950 1450 350 700 
L 275 800 
Q 975 1925 1325 3475 
L 700 3475 
L 475 3425 
L 275 3625 
L 1325 3625 
Q 1325 4575 1300 5275 
L 1900 4975 
L 1675 4800 
L 1675 3625 
L 2000 3625 
L 2275 3900 
L 2700 3475 
L 1675 3475 
L 1675 2850 
Q 2150 2600 2312 2425 
Q 2475 2250 2475 2125 
Q 2475 2050 2400 1912 
Q 2325 1775 2275 1775 
Q 2200 1775 2100 2075 
Q 1925 2475 1675 2725 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-503c" d="M 1525 650 
Q 1525 -225 1550 -525 
L 1175 -650 
Q 1200 250 1200 550 
L 1200 3025 
Q 750 2300 325 1850 
L 250 1900 
Q 725 2625 1100 3525 
Q 1475 4425 1675 5200 
L 2200 4875 
L 1950 4725 
Q 1550 3800 1425 3475 
L 1725 3300 
L 1525 3150 
L 1525 650 
z
M 2875 3225 
L 2875 2475 
L 4800 2475 
L 4800 3225 
L 2875 3225 
z
M 2875 2325 
L 2875 1575 
L 4800 1575 
L 4800 2325 
L 2875 2325 
z
M 2875 1425 
L 2875 675 
L 4800 675 
L 4800 1425 
L 2875 1425 
z
M 2875 525 
L 2875 -225 
L 4800 -225 
L 4800 525 
L 2875 525 
z
M 3550 4250 
Q 3575 4825 3550 5225 
L 4125 4950 
L 3925 4800 
L 3900 4250 
L 5025 4250 
L 5350 4575 
L 5825 4100 
L 3900 4100 
L 3875 3375 
L 4750 3375 
L 4975 3625 
L 5350 3300 
L 5125 3150 
L 5125 -225 
L 5350 -225 
L 5675 100 
L 6150 -375 
L 2450 -375 
Q 2125 -375 1850 -450 
L 1625 -225 
L 2550 -225 
L 2550 2550 
Q 2550 2850 2525 3575 
L 2875 3375 
L 3525 3375 
L 3550 4100 
L 2800 4100 
Q 2475 4100 2200 4025 
L 1975 4250 
L 3550 4250 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-78"/>
      <use xlink:href="#SimSun-5750" x="50"/>
      <use xlink:href="#SimSun-6807" x="150"/>
      <use xlink:href="#SimSun-503c" x="250"/>
     </g>
    </g>
   </g>
   <g id="line2d_27">
    <path d="M 80.745739 440.250061 
L 136.084582 198.330061 
L 191.423425 44.51625 
L 246.762268 163.983872 
L 302.101111 44.51625 
L 357.439954 447.71625 
L 412.778796 110.223872 
L 468.117639 376.03625 
L 523.456482 241.63625 
L 578.795325 92.301005 
L 634.134168 135.610061 
L 689.473011 307.341005 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_28">
    <path d="M 80.745739 440.250061 
L 136.084582 195.82714 
L 191.423425 71.296455 
L 246.762268 166.819613 
L 302.101111 91.349653 
L 357.439954 430.184566 
L 412.778796 125.652431 
L 468.117639 380.004543 
L 523.456482 248.913734 
L 578.795325 92.422554 
L 634.134168 155.573006 
L 689.473011 308.200725 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
   </g>
   <g id="line2d_29">
    <path d="M 80.745739 227.093332 
L 136.084582 188.744004 
L 191.423425 332.301886 
L 246.762268 264.034744 
L 302.101111 247.612817 
L 357.439954 363.381376 
L 412.778796 206.499368 
L 468.117639 155.476113 
L 523.456482 310.596357 
L 578.795325 255.905782 
L 634.134168 168.156261 
L 689.473011 269.60947 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke-dasharray: 2,3.3; stroke-dashoffset: 0; stroke: #008000; stroke-width: 2"/>
   </g>
   <g id="line2d_30">
    <path d="M 80.745739 227.093332 
L 136.084582 188.744004 
L 191.423425 332.301886 
L 246.762268 253.043492 
L 302.101111 258.173363 
L 357.439954 301.832706 
L 412.778796 270.382076 
L 468.117639 243.242418 
L 523.456482 258.988303 
L 578.795325 232.119405 
L 634.134168 222.533628 
L 689.473011 251.066968 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke-dasharray: 12.8,3.2,2,3.2; stroke-dashoffset: 0; stroke: #ffa500; stroke-width: 2"/>
   </g>
   <g id="line2d_31">
    <path d="M 80.745739 227.093332 
L 136.084582 215.588534 
L 191.423425 250.60254 
L 246.762268 254.632201 
L 302.101111 252.526386 
L 357.439954 285.782883 
L 412.778796 261.997828 
L 468.117639 230.041314 
L 523.456482 254.207827 
L 578.795325 254.717213 
L 634.134168 228.748928 
L 689.473011 241.007091 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #800080; stroke-width: 2"/>
   </g>
   <g id="line2d_32">
    <path d="M 80.745739 227.093332 
L 136.084582 188.744004 
L 191.423425 270.947119 
L 246.762268 276.809376 
L 302.101111 278.408476 
L 357.439954 278.126073 
L 412.778796 242.192162 
L 468.117639 227.538306 
L 523.456482 243.980485 
L 578.795325 240.621395 
L 634.134168 168.156261 
L 689.473011 269.60947 
" clip-path="url(#p73b1b5e053)" style="fill: none; stroke: #a52a2a; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 50.309375 467.87625 
L 50.309375 24.35625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 719.909375 467.87625 
L 719.909375 24.35625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 50.309375 467.87625 
L 719.909375 467.87625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 50.309375 24.35625 
L 719.909375 24.35625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_16">
    <!-- 卡尔曼滤波+LSTM vs 其他优化方法预测效果比较 -->
    <g transform="translate(234.609375 18.35625)scale(0.14 -0.14)">
     <defs>
      <path id="SimSun-5361" d="M 2675 -600 
Q 2700 75 2700 475 
L 2700 2675 
L 1150 2675 
Q 825 2675 550 2600 
L 325 2825 
L 2700 2825 
L 2700 4400 
Q 2700 4800 2675 5250 
L 3275 4950 
L 3050 4775 
L 3050 4100 
L 4650 4100 
L 4975 4425 
L 5450 3950 
L 3050 3950 
L 3050 2825 
L 5175 2825 
L 5550 3200 
L 6075 2675 
L 2800 2675 
L 3275 2400 
L 3050 2250 
L 3050 1975 
Q 4875 1550 5100 1300 
Q 5325 1050 5287 862 
Q 5250 675 5175 675 
Q 5075 675 4850 875 
Q 4475 1225 3050 1850 
L 3050 350 
Q 3050 -100 3075 -425 
L 2675 -600 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5c14" d="M 4650 3050 
L 5100 3900 
L 1925 3900 
Q 1375 3100 825 2575 
L 750 2650 
Q 1225 3250 1562 3925 
Q 1900 4600 2025 5225 
L 2550 4975 
Q 2425 4850 2300 4650 
Q 2175 4450 1975 4050 
L 5100 4050 
L 5375 4350 
L 5850 3875 
Q 5450 3850 4725 3000 
L 4650 3050 
z
M 3150 325 
L 3150 2300 
Q 3150 3025 3125 3400 
L 3700 3100 
L 3500 2950 
L 3500 125 
Q 3450 -425 2925 -625 
Q 2900 -275 2125 -25 
L 2125 100 
Q 2825 0 2975 12 
Q 3125 25 3150 325 
z
M 2000 2550 
L 2550 2175 
Q 2350 2075 2225 1875 
Q 2100 1675 1625 1062 
Q 1150 450 425 -100 
L 375 -25 
Q 975 575 1400 1250 
Q 1825 1925 2000 2550 
z
M 4100 2425 
Q 4800 1875 5212 1437 
Q 5625 1000 5700 737 
Q 5775 475 5675 337 
Q 5575 200 5500 200 
Q 5400 200 5300 475 
Q 5125 900 4812 1375 
Q 4500 1850 4025 2350 
L 4100 2425 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-66fc" d="M 2125 1275 
Q 2600 800 3175 475 
Q 3725 800 4175 1275 
L 2125 1275 
z
M 4850 4675 
Q 4850 3700 4875 3325 
L 4525 3175 
L 4525 3400 
L 1900 3400 
L 1900 3275 
L 1550 3150 
Q 1575 3600 1575 4075 
Q 1575 4550 1550 5125 
L 1900 4925 
L 4475 4925 
L 4700 5150 
L 5075 4850 
L 4850 4675 
z
M 1900 4775 
L 1900 4250 
L 4525 4250 
L 4525 4775 
L 1900 4775 
z
M 1900 4100 
L 1900 3550 
L 4525 3550 
L 4525 4100 
L 1900 4100 
z
M 900 1575 
Q 925 2000 925 2400 
Q 925 2800 900 3150 
L 1275 2975 
L 5050 2975 
L 5275 3200 
L 5650 2900 
L 5425 2750 
Q 5425 2050 5450 1775 
L 5100 1625 
L 5100 1875 
L 1250 1875 
L 1250 1725 
L 900 1575 
z
M 1250 2825 
L 1250 2025 
L 2275 2025 
L 2275 2825 
L 1250 2825 
z
M 2625 2825 
L 2625 2025 
L 3700 2025 
L 3700 2825 
L 2625 2825 
z
M 4025 2825 
L 4025 2025 
L 5100 2025 
L 5100 2825 
L 4025 2825 
z
M 4525 1100 
Q 3875 575 3450 325 
Q 3900 100 4487 12 
Q 5075 -75 5425 -75 
Q 5750 -75 6075 -25 
L 6075 -125 
Q 5600 -250 5575 -500 
Q 4850 -475 4237 -312 
Q 3625 -150 3175 150 
Q 2475 -200 1737 -362 
Q 1000 -525 400 -600 
L 350 -500 
Q 1000 -400 1687 -187 
Q 2375 25 2925 325 
Q 2425 725 2000 1275 
L 1775 1275 
L 1475 1225 
L 1275 1425 
L 4200 1425 
L 4450 1625 
L 4825 1200 
L 4525 1100 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6ee4" d="M 3025 1175 
L 3125 1150 
Q 3125 575 3050 337 
Q 2975 100 2825 50 
Q 2675 0 2562 37 
Q 2450 75 2450 125 
Q 2450 225 2600 350 
Q 2825 550 3025 1175 
z
M 3950 1775 
Q 4475 1550 4587 1412 
Q 4700 1275 4700 1175 
Q 4700 1050 4637 925 
Q 4575 800 4525 800 
Q 4450 800 4375 1025 
Q 4250 1350 3900 1700 
L 3950 1775 
z
M 5275 1175 
Q 5725 925 5875 762 
Q 6025 600 6025 450 
Q 6025 325 5950 200 
Q 5875 75 5850 75 
Q 5775 75 5725 300 
Q 5625 650 5225 1125 
L 5275 1175 
z
M 3925 -350 
Q 3475 -375 3450 75 
L 3450 850 
Q 3450 1175 3425 1475 
L 3975 1200 
L 3775 1050 
L 3775 200 
Q 3750 -75 4100 -75 
L 4825 -75 
Q 5025 -50 5050 212 
Q 5075 475 5100 800 
L 5200 800 
Q 5200 475 5237 250 
Q 5275 25 5500 -50 
Q 5325 -350 4875 -350 
L 3925 -350 
z
M 3725 3750 
Q 3725 4875 3700 5250 
L 4275 5025 
L 4050 4825 
L 4050 4450 
L 4950 4450 
L 5275 4775 
L 5700 4300 
L 4050 4300 
L 4050 3750 
L 5450 3750 
L 5650 4000 
L 6050 3575 
Q 5750 3600 5325 3100 
L 5250 3150 
L 5500 3600 
L 2700 3600 
Q 2725 2000 2525 1112 
Q 2325 225 1475 -575 
L 1400 -525 
Q 1875 25 2087 662 
Q 2300 1300 2337 1962 
Q 2375 2625 2375 3075 
Q 2375 3525 2350 3975 
L 2700 3750 
L 3725 3750 
z
M 5125 2200 
Q 5300 2250 5325 2525 
Q 5350 2775 5375 2975 
L 5475 2975 
Q 5475 2700 5512 2487 
Q 5550 2275 5750 2200 
Q 5575 1925 5150 1950 
L 4225 1950 
Q 3650 1925 3700 2325 
L 3700 2725 
L 3075 2675 
L 2950 2600 
L 2750 2800 
L 3700 2875 
Q 3700 3275 3675 3600 
L 4225 3375 
L 4025 3225 
L 4025 2925 
L 4575 3000 
L 4825 3275 
L 5225 2900 
L 4025 2775 
L 4025 2375 
Q 4025 2175 4350 2200 
L 5125 2200 
z
M 2200 4025 
Q 1400 1400 1337 1062 
Q 1275 725 1275 275 
Q 1275 -175 1275 -325 
Q 1275 -475 1175 -475 
Q 1075 -475 925 -412 
Q 775 -350 775 -175 
Q 775 -50 850 200 
Q 925 450 925 600 
Q 925 775 812 875 
Q 700 975 325 1075 
L 325 1150 
Q 775 1125 875 1150 
Q 975 1175 1112 1375 
Q 1250 1575 2100 4050 
L 2200 4025 
z
M 350 3475 
Q 1125 3100 1162 2887 
Q 1200 2675 1100 2550 
Q 1000 2425 950 2425 
Q 850 2425 800 2625 
Q 675 2950 300 3400 
L 350 3475 
z
M 925 4900 
Q 1650 4575 1725 4362 
Q 1800 4150 1687 3987 
Q 1575 3825 1525 3825 
Q 1450 3825 1375 4050 
Q 1250 4375 875 4850 
L 925 4900 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6ce2" d="M 4975 3175 
L 5300 3800 
L 4200 3800 
L 4200 2525 
L 5000 2525 
L 5225 2750 
L 5575 2400 
L 5325 2225 
Q 4975 1450 4400 750 
Q 5200 25 6075 -150 
L 6075 -225 
Q 5700 -250 5525 -525 
Q 4750 -75 4200 525 
Q 3325 -325 1950 -625 
L 1925 -525 
Q 3175 -125 4000 750 
Q 3500 1400 3225 2375 
L 3075 2375 
L 2925 2325 
L 2775 2450 
Q 2775 1550 2450 787 
Q 2125 25 1350 -600 
L 1275 -525 
Q 1950 200 2200 925 
Q 2450 1650 2450 2550 
Q 2450 3450 2425 4175 
L 2800 3950 
L 3875 3950 
Q 3875 4950 3850 5250 
L 4375 5050 
L 4200 4850 
L 4200 3950 
L 5250 3950 
L 5525 4225 
L 5950 3800 
Q 5725 3725 5562 3625 
Q 5400 3525 5050 3125 
L 4975 3175 
z
M 2775 3800 
L 2775 2525 
L 3875 2525 
L 3875 3800 
L 2775 3800 
z
M 4200 975 
Q 4675 1550 5025 2375 
L 3350 2375 
Q 3725 1450 4200 975 
z
M 2350 4100 
Q 1375 1450 1287 1137 
Q 1200 825 1212 375 
Q 1225 -75 1250 -250 
Q 1250 -350 1125 -350 
Q 1025 -350 875 -300 
Q 725 -250 725 -75 
Q 725 50 800 300 
Q 875 550 875 700 
Q 875 875 762 975 
Q 650 1075 275 1175 
L 275 1275 
Q 725 1225 837 1237 
Q 950 1250 1087 1462 
Q 1225 1675 2250 4125 
L 2350 4100 
z
M 325 3650 
Q 1200 3200 1200 2912 
Q 1200 2625 1062 2525 
Q 925 2425 825 2775 
Q 700 3100 275 3575 
L 325 3650 
z
M 875 5075 
Q 1575 4725 1637 4550 
Q 1700 4375 1700 4300 
Q 1700 4175 1612 4075 
Q 1525 3975 1475 3975 
Q 1400 3975 1325 4225 
Q 1200 4550 825 5000 
L 875 5075 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-2b" d="M 1525 2450 
L 1525 3875 
L 1750 3875 
L 1750 2450 
L 3000 2450 
L 3000 2225 
L 1750 2225 
L 1750 800 
L 1525 800 
L 1525 2225 
L 275 2225 
L 275 2450 
L 1525 2450 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-4c" d="M 1000 450 
Q 1000 350 1062 300 
Q 1125 250 1225 250 
L 1900 250 
Q 2250 250 2500 462 
Q 2750 675 2875 1125 
L 3000 1075 
L 2775 100 
L 200 100 
L 200 250 
L 375 250 
Q 500 250 550 300 
Q 600 350 600 450 
L 600 4025 
Q 600 4125 550 4175 
Q 500 4225 375 4225 
L 225 4225 
L 225 4375 
L 1400 4375 
L 1400 4225 
L 1225 4225 
Q 1125 4225 1062 4175 
Q 1000 4125 1000 4025 
L 1000 450 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-53" d="M 1500 2150 
Q 825 2450 575 2675 
Q 325 2900 325 3375 
Q 325 3750 637 4087 
Q 950 4425 1525 4425 
Q 1775 4425 2000 4350 
Q 2225 4250 2325 4250 
Q 2400 4250 2462 4287 
Q 2525 4325 2575 4425 
L 2675 3400 
L 2550 3350 
Q 2325 3875 2087 4062 
Q 1850 4250 1475 4250 
Q 1000 4250 825 3987 
Q 650 3725 650 3425 
Q 650 3150 812 2987 
Q 975 2825 1700 2500 
Q 2300 2250 2562 1937 
Q 2825 1625 2825 1200 
Q 2825 750 2512 400 
Q 2200 50 1600 50 
Q 1350 50 1150 150 
Q 925 225 800 225 
Q 725 225 650 175 
Q 575 125 475 50 
L 275 1275 
L 425 1325 
Q 600 700 912 462 
Q 1225 225 1625 225 
Q 2050 225 2262 475 
Q 2475 725 2475 1200 
Q 2475 1450 2237 1687 
Q 2000 1925 1500 2150 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-54" d="M 2225 100 
L 925 100 
L 925 250 
L 1150 250 
Q 1275 250 1325 300 
Q 1375 350 1375 450 
L 1375 3950 
Q 1375 4150 1350 4175 
Q 1325 4200 1150 4200 
Q 800 4200 662 4075 
Q 525 3950 325 3400 
L 175 3425 
L 400 4375 
L 2775 4375 
L 2975 3425 
L 2850 3400 
Q 2650 3925 2537 4062 
Q 2425 4200 2075 4200 
Q 1825 4200 1800 4175 
Q 1775 4150 1775 3925 
L 1775 450 
Q 1775 350 1837 300 
Q 1900 250 2000 250 
L 2225 250 
L 2225 100 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-4d" d="M 2800 450 
Q 2800 350 2862 300 
Q 2925 250 3025 250 
L 3125 250 
L 3125 100 
L 2050 100 
L 2050 250 
L 2175 250 
Q 2300 250 2350 300 
Q 2400 350 2400 450 
L 2400 3725 
L 2350 3725 
L 1500 100 
L 1350 100 
L 575 3650 
L 525 3650 
L 525 450 
Q 525 350 587 300 
Q 650 250 750 250 
L 825 250 
L 825 100 
L 75 100 
L 75 250 
L 150 250 
Q 275 250 325 300 
Q 375 350 375 450 
L 375 4025 
Q 375 4125 325 4175 
Q 275 4225 150 4225 
L 50 4225 
L 50 4375 
L 775 4375 
L 1500 1025 
L 1550 1025 
L 2325 4375 
L 3125 4375 
L 3125 4225 
L 3025 4225 
Q 2925 4225 2862 4175 
Q 2800 4125 2800 4025 
L 2800 450 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-20" d="M 0 0 
Q 0 0 0 0 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-76" d="M 1625 75 
L 1475 75 
L 625 2525 
Q 600 2625 525 2687 
Q 450 2750 350 2750 
L 225 2750 
L 225 2900 
L 1300 2900 
L 1300 2750 
L 1200 2750 
Q 1050 2750 1025 2700 
Q 1000 2650 1000 2600 
Q 1000 2500 1025 2425 
L 1625 675 
L 1675 675 
L 2275 2400 
Q 2325 2525 2325 2575 
Q 2325 2600 2275 2675 
Q 2225 2750 2075 2750 
L 2025 2750 
L 2025 2900 
L 2925 2900 
L 2925 2750 
L 2875 2750 
Q 2725 2750 2637 2662 
Q 2550 2575 2475 2400 
L 1625 75 
z
M 2600 2750 
L 2600 2750 
z
M 2400 2750 
L 2400 2750 
z
M 925 2750 
L 925 2750 
z
M 550 2750 
L 550 2750 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-73" d="M 625 2175 
Q 625 2550 912 2750 
Q 1200 2950 1600 2950 
Q 1825 2950 2025 2900 
Q 2225 2825 2300 2825 
Q 2375 2825 2425 2850 
Q 2475 2875 2550 2950 
L 2650 2125 
L 2500 2100 
Q 2400 2425 2162 2612 
Q 1925 2800 1625 2800 
Q 1300 2800 1112 2675 
Q 925 2550 925 2325 
Q 925 2125 1012 2012 
Q 1100 1900 1325 1850 
Q 1500 1775 1775 1700 
Q 2025 1600 2250 1500 
Q 2450 1400 2587 1237 
Q 2725 1075 2725 875 
Q 2725 500 2462 287 
Q 2200 75 1700 75 
Q 1375 75 1225 150 
Q 1050 200 950 200 
Q 875 200 787 162 
Q 700 125 600 75 
L 525 1000 
L 675 1025 
Q 750 625 1000 425 
Q 1250 225 1700 225 
Q 2050 225 2237 350 
Q 2425 475 2425 725 
Q 2425 900 2325 1012 
Q 2225 1125 2125 1175 
Q 1875 1275 1575 1400 
Q 1275 1500 1050 1600 
Q 825 1700 725 1850 
Q 625 2000 625 2175 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5176" d="M 4025 4275 
Q 4025 4775 4000 5250 
L 4575 4975 
L 4375 4775 
L 4375 4275 
L 4975 4275 
L 5325 4625 
L 5825 4125 
L 4375 4125 
L 4375 1300 
L 5125 1300 
L 5550 1725 
L 6125 1150 
L 1100 1150 
Q 775 1150 500 1075 
L 275 1300 
L 1975 1300 
L 1975 4125 
L 1425 4125 
Q 1100 4125 825 4050 
L 600 4275 
L 1975 4275 
Q 1975 4675 1950 5225 
L 2525 4975 
L 2325 4825 
L 2325 4275 
L 4025 4275 
z
M 2325 4125 
L 2325 3325 
L 4025 3325 
L 4025 4125 
L 2325 4125 
z
M 2325 3175 
L 2325 2350 
L 4025 2350 
L 4025 3175 
L 2325 3175 
z
M 2325 2200 
L 2325 1300 
L 4025 1300 
L 4025 2200 
L 2325 2200 
z
M 2325 1075 
L 2825 650 
Q 2625 625 1975 150 
Q 1300 -325 475 -600 
L 425 -525 
Q 925 -250 1525 225 
Q 2125 700 2325 1075 
z
M 3750 900 
L 3825 975 
Q 5275 300 5412 112 
Q 5550 -75 5550 -250 
Q 5550 -350 5500 -475 
Q 5450 -600 5400 -600 
Q 5300 -600 5125 -375 
Q 4850 0 3750 900 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-4ed6" d="M 5450 3425 
Q 5475 1700 5350 1462 
Q 5225 1225 4900 1125 
Q 4950 1400 4350 1550 
L 4350 1650 
Q 4575 1625 4750 1625 
Q 4875 1625 4962 1650 
Q 5050 1675 5075 1950 
Q 5100 2225 5125 3450 
L 4100 3200 
L 4100 725 
L 3775 525 
L 3775 3100 
L 2925 2875 
L 2925 175 
Q 2925 -125 3200 -125 
L 5400 -125 
Q 5575 -25 5612 287 
Q 5650 600 5650 1025 
L 5775 1025 
Q 5775 600 5837 312 
Q 5900 25 6150 -25 
Q 5975 -400 5325 -400 
L 3200 -400 
Q 2575 -425 2600 75 
L 2600 2800 
L 2250 2700 
L 2100 2600 
L 1850 2775 
L 2600 2950 
Q 2600 3500 2575 4050 
L 3175 3800 
L 2925 3625 
L 2925 3025 
L 3775 3250 
Q 3775 4375 3750 5225 
L 4325 4975 
L 4100 4800 
L 4100 3350 
L 5050 3600 
L 5200 3875 
L 5625 3600 
L 5450 3425 
z
M 1300 -625 
Q 1325 125 1325 525 
L 1325 3200 
Q 725 2150 325 1725 
L 250 1775 
Q 825 2725 1200 3700 
Q 1575 4675 1700 5275 
L 2275 4975 
Q 2075 4875 1875 4437 
Q 1675 4000 1450 3475 
L 1875 3250 
L 1675 3100 
L 1675 -450 
L 1300 -625 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-4f18" d="M 1275 -600 
Q 1300 125 1300 600 
L 1300 3275 
Q 725 2450 300 2025 
L 225 2100 
Q 650 2675 1087 3562 
Q 1525 4450 1750 5225 
L 2275 4900 
Q 2000 4725 1425 3550 
L 1850 3325 
L 1650 3125 
L 1650 475 
Q 1650 225 1675 -400 
L 1275 -600 
z
M 4275 4875 
Q 5225 4550 5287 4400 
Q 5350 4250 5350 4150 
Q 5350 4025 5287 3900 
Q 5225 3775 5175 3775 
Q 5075 3775 4975 4000 
Q 4775 4350 4225 4800 
L 4275 4875 
z
M 3275 3325 
L 3275 4175 
Q 3275 4700 3250 5175 
L 3850 4900 
L 3625 4700 
L 3625 3325 
L 5025 3325 
L 5350 3650 
L 5825 3175 
L 4325 3175 
L 4325 300 
Q 4325 -50 4675 -50 
L 5425 -50 
Q 5600 0 5650 350 
Q 5700 700 5700 1100 
L 5825 1100 
Q 5825 725 5862 387 
Q 5900 50 6125 0 
Q 5925 -350 5575 -350 
L 4575 -350 
Q 4000 -375 4000 125 
L 4000 3175 
L 3625 3175 
Q 3600 2275 3425 1587 
Q 3250 900 2850 362 
Q 2450 -175 1750 -650 
L 1675 -575 
Q 2200 -150 2550 387 
Q 2900 925 3075 1625 
Q 3250 2325 3275 3175 
L 2725 3175 
Q 2400 3175 2125 3100 
L 1900 3325 
L 3275 3325 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5316" d="M 1375 -600 
Q 1400 200 1400 925 
L 1400 3125 
Q 850 2300 350 1775 
L 275 1850 
Q 825 2625 1300 3650 
Q 1775 4675 1900 5200 
L 2450 4875 
L 2200 4725 
Q 1875 4000 1600 3475 
L 1950 3275 
L 1750 3075 
L 1750 350 
Q 1750 75 1775 -400 
L 1375 -600 
z
M 3450 2125 
L 3450 3925 
Q 3450 4475 3425 5200 
L 4000 4925 
L 3800 4725 
L 3800 2400 
Q 4150 2725 4562 3225 
Q 4975 3725 5150 4025 
L 5600 3600 
L 5325 3525 
Q 4600 2700 3800 2000 
L 3800 425 
Q 3800 0 4225 0 
L 5300 0 
Q 5500 0 5550 325 
Q 5600 650 5600 1325 
L 5725 1325 
Q 5775 650 5850 425 
Q 5925 200 6125 125 
Q 5900 -325 5400 -325 
L 4075 -325 
Q 3450 -325 3450 250 
L 3450 1775 
Q 2800 1275 1950 925 
L 1925 1025 
Q 2800 1525 3450 2125 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-65b9" d="M 275 3925 
L 5250 3925 
L 5625 4300 
L 6150 3775 
L 3075 3775 
Q 3050 3300 2950 2675 
L 4575 2675 
L 4825 2925 
L 5225 2550 
L 4975 2400 
Q 4725 475 4625 125 
Q 4525 -225 4312 -362 
Q 4100 -500 3725 -600 
Q 3625 -200 2900 100 
L 2925 200 
Q 3600 0 3812 -12 
Q 4025 -25 4137 75 
Q 4250 175 4350 712 
Q 4450 1250 4600 2525 
L 2925 2525 
Q 2500 350 475 -575 
L 425 -475 
Q 2675 800 2675 3775 
L 1100 3775 
Q 775 3775 500 3700 
L 275 3925 
z
M 2675 5150 
Q 3325 4900 3437 4725 
Q 3550 4550 3550 4450 
Q 3550 4350 3450 4212 
Q 3350 4075 3325 4075 
Q 3225 4075 3125 4350 
Q 2975 4725 2625 5075 
L 2675 5150 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6cd5" d="M 3800 3850 
Q 3800 4925 3775 5275 
L 4375 4975 
L 4125 4725 
L 4125 3850 
L 5100 3850 
L 5375 4125 
L 5775 3700 
L 4125 3700 
L 4125 2350 
L 5450 2350 
L 5750 2650 
L 6175 2200 
L 3825 2200 
L 4225 1925 
Q 4000 1825 3750 1450 
Q 3500 1075 2600 125 
L 5225 300 
Q 4900 750 4525 1175 
L 4575 1250 
Q 5025 925 5287 725 
Q 5550 525 5712 337 
Q 5875 150 5875 0 
Q 5875 -150 5775 -300 
Q 5675 -450 5650 -450 
Q 5575 -450 5525 -300 
Q 5425 -50 5300 175 
Q 3900 25 3175 -150 
Q 2450 -325 2350 -450 
L 2100 125 
Q 2425 125 2962 875 
Q 3500 1625 3700 2200 
L 2775 2200 
Q 2450 2200 2175 2125 
L 1950 2350 
L 3800 2350 
L 3800 3700 
L 3250 3700 
Q 2925 3700 2650 3625 
L 2425 3850 
L 3800 3850 
z
M 2375 4150 
Q 1400 1500 1312 1187 
Q 1225 875 1237 425 
Q 1250 -25 1275 -200 
Q 1275 -300 1150 -300 
Q 1050 -300 900 -250 
Q 750 -200 750 -25 
Q 750 100 825 350 
Q 900 600 900 750 
Q 900 925 787 1025 
Q 675 1125 300 1225 
L 300 1325 
Q 750 1275 862 1287 
Q 975 1300 1112 1512 
Q 1250 1725 2275 4175 
L 2375 4150 
z
M 325 3600 
Q 1200 3150 1200 2862 
Q 1200 2575 1062 2475 
Q 925 2375 825 2725 
Q 700 3050 275 3525 
L 325 3600 
z
M 900 5050 
Q 1600 4700 1662 4525 
Q 1725 4350 1725 4275 
Q 1725 4150 1637 4050 
Q 1550 3950 1500 3950 
Q 1425 3950 1350 4200 
Q 1225 4525 850 4975 
L 900 5050 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-9884" d="M 475 4825 
L 2225 4825 
L 2425 5075 
L 2825 4675 
Q 2600 4625 2387 4412 
Q 2175 4200 1800 3750 
Q 2050 3575 2037 3400 
Q 2025 3225 1937 3150 
Q 1850 3075 1825 3075 
Q 1725 3075 1650 3300 
Q 1525 3600 1050 4100 
L 1100 4150 
Q 1475 3975 1700 3825 
L 2225 4675 
L 1300 4675 
Q 975 4675 700 4600 
L 475 4825 
z
M 725 100 
Q 1175 50 1362 37 
Q 1550 25 1550 275 
L 1550 2875 
L 1125 2875 
Q 800 2875 525 2800 
L 300 3025 
L 2525 3025 
L 2725 3275 
L 3150 2875 
Q 2800 2825 2275 2075 
L 2200 2125 
L 2550 2875 
L 1875 2875 
L 1875 150 
Q 1875 -400 1350 -500 
Q 1325 -175 725 0 
L 725 100 
z
M 3250 775 
Q 3275 1425 3275 2287 
Q 3275 3150 3250 3900 
L 3600 3700 
L 4050 3700 
Q 4175 4400 4200 4675 
L 3650 4675 
Q 3325 4675 3050 4600 
L 2825 4825 
L 5325 4825 
L 5675 5150 
L 6125 4675 
L 4675 4675 
Q 4500 4425 4175 3700 
L 5300 3700 
L 5475 3975 
L 5875 3675 
L 5675 3475 
L 5675 1950 
Q 5675 1525 5700 1075 
L 5350 875 
L 5350 3550 
L 3600 3550 
L 3600 1000 
L 3250 775 
z
M 4850 2875 
L 4625 2675 
Q 4625 1475 4475 937 
Q 4325 400 3900 50 
Q 3475 -300 2425 -625 
L 2400 -550 
Q 3500 -50 3837 400 
Q 4175 850 4237 1587 
Q 4300 2325 4275 3175 
L 4850 2875 
z
M 4650 950 
Q 5725 375 5875 212 
Q 6025 50 6025 -125 
Q 6025 -225 5962 -387 
Q 5900 -550 5850 -550 
Q 5800 -550 5750 -450 
Q 5625 -250 5437 0 
Q 5250 250 4600 875 
L 4650 950 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6d4b" d="M 1975 1000 
Q 2000 1475 2000 2925 
Q 2000 4375 1975 5000 
L 2350 4775 
L 3575 4775 
L 3775 4975 
L 4100 4650 
L 3900 4500 
L 3900 2350 
Q 3900 1850 3925 1350 
L 3600 1200 
L 3600 4625 
L 2325 4625 
L 2325 1150 
L 1975 1000 
z
M 2775 3975 
L 3300 3700 
L 3125 3525 
Q 3150 1825 2875 937 
Q 2600 50 1375 -600 
L 1325 -500 
Q 2200 100 2475 725 
Q 2750 1350 2775 2187 
Q 2800 3025 2775 3975 
z
M 3100 950 
Q 3825 450 3937 262 
Q 4050 75 4050 -25 
Q 4050 -150 3975 -262 
Q 3900 -375 3850 -375 
Q 3750 -375 3650 -75 
Q 3500 375 3050 875 
L 3100 950 
z
M 4475 825 
Q 4500 1200 4500 1700 
L 4500 3400 
Q 4500 3950 4475 4300 
L 5025 4025 
L 4825 3825 
L 4825 1700 
Q 4825 1325 4850 950 
L 4475 825 
z
M 5375 150 
L 5375 4050 
Q 5375 4750 5350 5125 
L 5875 4875 
L 5700 4700 
L 5700 75 
Q 5700 -200 5600 -350 
Q 5500 -500 5200 -600 
Q 5025 -225 4450 -50 
L 4450 50 
Q 5125 -50 5250 -37 
Q 5375 -25 5375 150 
z
M 1850 3500 
Q 1375 1525 1300 1225 
Q 1200 900 1212 450 
Q 1225 0 1250 -175 
Q 1250 -275 1125 -275 
Q 1025 -275 875 -225 
Q 725 -175 725 0 
Q 725 125 800 375 
Q 875 625 875 775 
Q 875 950 762 1050 
Q 650 1150 275 1250 
L 275 1350 
Q 725 1300 837 1312 
Q 950 1325 1087 1537 
Q 1225 1750 1750 3525 
L 1850 3500 
z
M 375 3775 
Q 1075 3350 1112 3100 
Q 1150 2850 1050 2775 
Q 950 2700 900 2700 
Q 800 2700 725 2975 
Q 600 3350 325 3700 
L 375 3775 
z
M 800 5100 
Q 1475 4725 1550 4512 
Q 1625 4300 1525 4187 
Q 1425 4075 1350 4075 
Q 1275 4075 1200 4300 
Q 1075 4650 750 5025 
L 800 5100 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6548" d="M 575 4025 
L 1975 4025 
Q 1950 4250 1837 4500 
Q 1725 4750 1450 5100 
L 1525 5150 
Q 2000 4875 2175 4687 
Q 2350 4500 2350 4350 
Q 2350 4200 2200 4025 
L 2850 4025 
L 3150 4325 
L 3600 3875 
L 1400 3875 
Q 1075 3875 800 3800 
L 575 4025 
z
M 1425 3675 
L 1900 3350 
L 1675 3250 
Q 950 2225 475 1875 
L 400 1925 
Q 1075 2700 1425 3675 
z
M 2300 3575 
L 2350 3650 
Q 2900 3400 3075 3250 
Q 3250 3100 3250 2950 
Q 3250 2850 3187 2700 
Q 3125 2550 3075 2550 
Q 3000 2550 2900 2775 
Q 2725 3150 2300 3575 
z
M 1250 2225 
Q 1675 1950 2075 1625 
Q 2375 2250 2450 2650 
L 2900 2275 
Q 2725 2225 2300 1425 
Q 2700 1075 2862 862 
Q 3025 650 3025 475 
Q 3025 375 2962 237 
Q 2900 100 2875 100 
Q 2800 100 2700 350 
Q 2525 700 2150 1175 
Q 1425 100 475 -475 
L 425 -400 
Q 1300 300 1950 1400 
Q 1525 1850 1200 2150 
L 1250 2225 
z
M 4050 3500 
L 3950 3250 
Q 4150 2050 4525 1275 
Q 4950 2100 5125 3500 
L 4050 3500 
z
M 4175 5250 
L 4775 4950 
Q 4600 4875 4525 4725 
Q 4450 4575 4100 3650 
L 5375 3650 
L 5675 3950 
L 6125 3500 
L 5475 3500 
Q 5275 1975 4700 950 
Q 5350 0 6100 -125 
L 6100 -225 
Q 5650 -250 5550 -500 
Q 4975 -75 4475 725 
Q 3650 -125 2450 -625 
L 2400 -550 
Q 3650 125 4325 1000 
Q 3925 2025 3850 3050 
Q 3550 2400 3225 2000 
L 3150 2050 
Q 3825 3350 4175 5250 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-679c" d="M 1725 4725 
L 1725 3875 
L 3025 3875 
L 3025 4725 
L 1725 4725 
z
M 3400 4725 
L 3400 3875 
L 4725 3875 
L 4725 4725 
L 3400 4725 
z
M 1725 3725 
L 1725 2825 
L 3025 2825 
L 3025 3725 
L 1725 3725 
z
M 3400 3725 
L 3400 2825 
L 4725 2825 
L 4725 3725 
L 3400 3725 
z
M 1350 2300 
Q 1375 3000 1375 3700 
Q 1375 4400 1350 5075 
L 1725 4875 
L 4700 4875 
L 4925 5100 
L 5300 4750 
L 5075 4600 
Q 5075 2850 5100 2500 
L 4725 2325 
L 4725 2675 
L 3400 2675 
L 3400 1950 
L 5100 1950 
L 5475 2325 
L 6000 1800 
L 3450 1800 
Q 4500 450 6025 100 
L 6025 0 
Q 5550 -25 5450 -300 
Q 3950 525 3400 1700 
Q 3400 -50 3425 -425 
L 3000 -625 
Q 3025 350 3025 1650 
Q 2100 325 400 -375 
L 375 -275 
Q 1850 550 2700 1800 
L 1300 1800 
Q 975 1800 700 1725 
L 475 1950 
L 3025 1950 
L 3025 2675 
L 1725 2675 
L 1725 2450 
L 1350 2300 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6bd4" d="M 1050 3400 
Q 1050 4525 1025 5075 
L 1625 4800 
L 1400 4600 
L 1400 2925 
L 2400 2925 
L 2700 3225 
L 3150 2775 
L 1400 2775 
L 1400 350 
L 3050 925 
L 3100 825 
Q 1625 150 1250 -250 
L 950 175 
Q 1050 275 1050 525 
L 1050 3400 
z
M 3425 3825 
Q 3425 4575 3400 5125 
L 4025 4825 
L 3775 4625 
L 3775 2525 
Q 4300 2925 4712 3400 
Q 5125 3875 5275 4175 
L 5775 3700 
Q 5475 3650 5025 3250 
Q 4550 2850 3775 2375 
L 3775 425 
Q 3775 125 4100 125 
L 5125 125 
Q 5325 125 5400 350 
Q 5475 575 5500 1425 
L 5625 1425 
Q 5625 825 5712 550 
Q 5800 275 6000 175 
Q 5875 -25 5712 -112 
Q 5550 -200 5275 -200 
L 3975 -200 
Q 3425 -200 3425 275 
L 3425 3825 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-8f83" d="M 2925 1475 
L 1950 1125 
Q 1950 150 1975 -350 
L 1600 -550 
Q 1625 425 1625 1025 
Q 675 675 550 525 
L 300 975 
Q 700 1025 1625 1250 
L 1625 2325 
L 975 2325 
L 775 2125 
L 450 2475 
L 675 2650 
L 1100 3900 
L 1075 3900 
Q 750 3900 475 3825 
L 250 4050 
L 1125 4050 
Q 1375 4975 1400 5250 
L 1900 4975 
L 1700 4825 
Q 1600 4575 1450 4050 
L 2200 4050 
L 2450 4300 
L 2825 3900 
L 1425 3900 
L 975 2475 
L 1625 2475 
Q 1625 2950 1600 3450 
L 2150 3200 
L 1950 3025 
L 1950 2475 
L 2175 2475 
L 2400 2700 
L 2750 2325 
L 1950 2325 
L 1950 1325 
L 2900 1575 
L 2925 1475 
z
M 3850 5125 
L 3875 5200 
Q 4400 4950 4487 4812 
Q 4575 4675 4575 4575 
Q 4575 4475 4475 4362 
Q 4375 4250 4350 4250 
Q 4275 4250 4200 4500 
Q 4100 4825 3850 5125 
z
M 2850 4125 
L 5250 4125 
L 5550 4425 
L 5975 3975 
L 3550 3975 
Q 3350 3975 3075 3900 
L 2850 4125 
z
M 3700 3775 
L 4125 3400 
L 3900 3325 
Q 3400 2750 2825 2350 
L 2775 2425 
Q 3125 2775 3362 3137 
Q 3600 3500 3700 3775 
z
M 4875 3675 
Q 5625 3300 5737 3125 
Q 5850 2950 5850 2825 
Q 5850 2725 5775 2625 
Q 5700 2525 5650 2525 
Q 5600 2525 5525 2700 
Q 5400 2975 5250 3162 
Q 5100 3350 4850 3600 
L 4875 3675 
z
M 5200 2450 
Q 4850 1375 4500 825 
Q 5125 50 6125 -175 
L 6125 -250 
Q 5800 -325 5675 -550 
Q 4850 -175 4325 575 
Q 3500 -300 2125 -575 
L 2125 -475 
Q 3450 -25 4150 825 
Q 3725 1550 3475 2675 
L 3600 2700 
Q 3875 1725 4300 1100 
Q 4500 1475 4637 1887 
Q 4775 2300 4900 2900 
L 5400 2575 
L 5200 2450 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimSun-5361"/>
     <use xlink:href="#SimSun-5c14" x="100"/>
     <use xlink:href="#SimSun-66fc" x="200"/>
     <use xlink:href="#SimSun-6ee4" x="300"/>
     <use xlink:href="#SimSun-6ce2" x="400"/>
     <use xlink:href="#SimSun-2b" x="500"/>
     <use xlink:href="#SimSun-4c" x="550"/>
     <use xlink:href="#SimSun-53" x="600"/>
     <use xlink:href="#SimSun-54" x="650"/>
     <use xlink:href="#SimSun-4d" x="700"/>
     <use xlink:href="#SimSun-20" x="750"/>
     <use xlink:href="#SimSun-76" x="800"/>
     <use xlink:href="#SimSun-73" x="850"/>
     <use xlink:href="#SimSun-20" x="900"/>
     <use xlink:href="#SimSun-5176" x="950"/>
     <use xlink:href="#SimSun-4ed6" x="1050"/>
     <use xlink:href="#SimSun-4f18" x="1150"/>
     <use xlink:href="#SimSun-5316" x="1250"/>
     <use xlink:href="#SimSun-65b9" x="1350"/>
     <use xlink:href="#SimSun-6cd5" x="1450"/>
     <use xlink:href="#SimSun-9884" x="1550"/>
     <use xlink:href="#SimSun-6d4b" x="1650"/>
     <use xlink:href="#SimSun-6548" x="1750"/>
     <use xlink:href="#SimSun-679c" x="1850"/>
     <use xlink:href="#SimSun-6bd4" x="1950"/>
     <use xlink:href="#SimSun-8f83" x="2050"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="line2d_33">
     <path d="M 576.059375 379.036406 
L 586.559375 379.036406 
L 597.059375 379.036406 
" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_17">
     <!-- 观测的x中心值 -->
     <g transform="translate(605.459375 382.711406)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-89c2" d="M 2375 4100 
Q 2175 2925 1925 2175 
Q 2450 1375 2525 1087 
Q 2600 800 2600 725 
Q 2600 550 2525 437 
Q 2450 325 2425 325 
Q 2350 325 2200 800 
Q 2025 1375 1750 1875 
Q 1275 725 300 -200 
L 250 -100 
Q 1125 975 1550 2150 
Q 1300 2600 625 3475 
L 700 3525 
Q 1325 2900 1675 2475 
Q 1850 2975 2025 4200 
L 1200 4200 
Q 875 4200 600 4125 
L 375 4350 
L 2000 4350 
L 2250 4600 
L 2575 4275 
L 2375 4100 
z
M 5475 4500 
Q 5475 2600 5500 1900 
L 5150 1725 
L 5150 4600 
L 3200 4600 
L 3200 1875 
L 2850 1650 
Q 2875 2375 2875 3250 
Q 2875 4125 2850 4950 
L 3225 4750 
L 5100 4750 
L 5350 5000 
L 5700 4650 
L 5475 4500 
z
M 1525 -525 
Q 2250 -275 2775 112 
Q 3300 500 3550 1087 
Q 3800 1675 3850 2625 
Q 3900 3575 3900 4100 
L 4475 3775 
L 4250 3600 
Q 4225 2050 3987 1312 
Q 3750 575 3225 137 
Q 2700 -300 1550 -625 
L 1525 -525 
z
M 4675 -400 
Q 4250 -400 4250 100 
L 4250 1350 
Q 4250 1725 4225 2100 
L 4750 1825 
L 4575 1650 
L 4575 150 
Q 4575 -100 4850 -100 
L 5300 -100 
Q 5525 -100 5575 175 
Q 5625 450 5650 1125 
L 5775 1125 
Q 5775 500 5837 262 
Q 5900 25 6075 -25 
Q 5900 -400 5525 -400 
L 4675 -400 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-7684" d="M 1125 3775 
L 1125 2175 
L 2425 2175 
L 2425 3775 
L 1125 3775 
z
M 1125 2025 
L 1125 275 
L 2425 275 
L 2425 2025 
L 1125 2025 
z
M 2750 3625 
Q 2750 700 2775 -200 
L 2425 -400 
L 2425 125 
L 1125 125 
L 1125 -300 
L 775 -500 
Q 800 675 800 1837 
Q 800 3000 775 4150 
L 1150 3925 
L 1400 3925 
Q 1650 4775 1675 5225 
L 2225 4925 
Q 1975 4800 1550 3925 
L 2375 3925 
L 2625 4175 
L 3000 3800 
L 2750 3625 
z
M 3975 5200 
L 4525 4875 
Q 4325 4750 3900 3850 
L 5250 3850 
L 5525 4125 
L 5900 3750 
L 5675 3575 
Q 5625 650 5537 262 
Q 5450 -125 5287 -275 
Q 5125 -425 4775 -550 
Q 4650 -150 3950 50 
L 3950 175 
Q 4575 50 4850 50 
Q 5125 50 5187 475 
Q 5250 900 5300 3700 
L 3850 3700 
Q 3400 2975 2900 2475 
L 2825 2525 
Q 3350 3325 3600 3950 
Q 3850 4575 3975 5200 
z
M 3525 2750 
Q 4125 2300 4225 2087 
Q 4325 1875 4325 1775 
Q 4325 1600 4225 1475 
Q 4125 1350 4075 1350 
Q 3975 1350 3925 1700 
Q 3825 2200 3475 2675 
L 3525 2750 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-4e2d" d="M 3325 3625 
L 3325 1975 
L 5125 1975 
L 5125 3625 
L 3325 3625 
z
M 2950 3775 
Q 2950 4750 2925 5250 
L 3525 4975 
L 3325 4775 
L 3325 3775 
L 5075 3775 
L 5275 4075 
L 5700 3750 
L 5475 3550 
L 5475 2100 
Q 5475 1825 5500 1525 
L 5125 1375 
L 5125 1825 
L 3325 1825 
Q 3325 -75 3350 -400 
L 2925 -625 
Q 2950 -50 2950 1825 
L 1225 1825 
L 1225 1525 
L 850 1325 
Q 875 1775 875 2625 
Q 875 3475 850 4000 
L 1225 3775 
L 2950 3775 
z
M 1225 3625 
L 1225 1975 
L 2950 1975 
L 2950 3625 
L 1225 3625 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5fc3" d="M 2750 4875 
Q 3350 4525 3625 4275 
Q 3900 4025 3862 3762 
Q 3825 3500 3725 3412 
Q 3625 3325 3575 3325 
Q 3450 3325 3350 3675 
Q 3200 4125 2675 4800 
L 2750 4875 
z
M 2025 3800 
L 2650 3475 
L 2425 3275 
L 2425 725 
Q 2400 225 2775 200 
L 4200 200 
Q 4475 225 4537 625 
Q 4600 1025 4625 1675 
L 4750 1675 
Q 4750 1000 4812 687 
Q 4875 375 5150 275 
Q 4950 -125 4425 -125 
L 2575 -125 
Q 2000 -100 2050 625 
L 2050 2675 
Q 2050 3325 2025 3800 
z
M 1225 2900 
L 1350 2900 
Q 1350 2100 1300 1725 
Q 1250 1350 1087 1225 
Q 925 1100 775 1100 
Q 700 1100 587 1137 
Q 475 1175 475 1250 
Q 475 1375 700 1625 
Q 1025 2000 1225 2900 
z
M 4850 2925 
L 4925 2975 
Q 5675 2300 5825 2050 
Q 5975 1800 5925 1550 
Q 5875 1300 5775 1237 
Q 5675 1175 5625 1175 
Q 5500 1175 5450 1525 
Q 5375 1975 4850 2925 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-89c2"/>
      <use xlink:href="#SimSun-6d4b" x="100"/>
      <use xlink:href="#SimSun-7684" x="200"/>
      <use xlink:href="#SimSun-78" x="300"/>
      <use xlink:href="#SimSun-4e2d" x="350"/>
      <use xlink:href="#SimSun-5fc3" x="450"/>
      <use xlink:href="#SimSun-503c" x="550"/>
     </g>
    </g>
    <g id="line2d_34">
     <path d="M 576.059375 393.966094 
L 586.559375 393.966094 
L 597.059375 393.966094 
" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
    </g>
    <g id="text_18">
     <!-- 卡尔曼滤波+LSTM的x值 -->
     <g transform="translate(605.459375 397.641094)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-5361"/>
      <use xlink:href="#SimSun-5c14" x="100"/>
      <use xlink:href="#SimSun-66fc" x="200"/>
      <use xlink:href="#SimSun-6ee4" x="300"/>
      <use xlink:href="#SimSun-6ce2" x="400"/>
      <use xlink:href="#SimSun-2b" x="500"/>
      <use xlink:href="#SimSun-4c" x="550"/>
      <use xlink:href="#SimSun-53" x="600"/>
      <use xlink:href="#SimSun-54" x="650"/>
      <use xlink:href="#SimSun-4d" x="700"/>
      <use xlink:href="#SimSun-7684" x="750"/>
      <use xlink:href="#SimSun-78" x="850"/>
      <use xlink:href="#SimSun-503c" x="900"/>
     </g>
    </g>
    <g id="line2d_35">
     <path d="M 576.059375 408.731719 
L 586.559375 408.731719 
L 597.059375 408.731719 
" style="fill: none; stroke-dasharray: 2,3.3; stroke-dashoffset: 0; stroke: #008000; stroke-width: 2"/>
    </g>
    <g id="text_19">
     <!-- 仅LSTM -->
     <g transform="translate(605.459375 412.406719)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-4ec5" d="M 2850 4300 
Q 3050 3275 3287 2600 
Q 3525 1925 3850 1425 
Q 4175 1925 4425 2650 
Q 4675 3375 4825 4300 
L 2850 4300 
z
M 5200 4175 
Q 4900 2825 4637 2212 
Q 4375 1600 4050 1150 
Q 4400 750 4887 400 
Q 5375 50 6025 -150 
L 6025 -225 
Q 5600 -275 5500 -525 
Q 4975 -225 4587 112 
Q 4200 450 3875 875 
Q 3400 325 2862 -25 
Q 2325 -375 1650 -625 
L 1600 -525 
Q 2250 -225 2762 200 
Q 3275 625 3675 1150 
Q 3275 1750 3050 2475 
Q 2825 3200 2700 4300 
L 2550 4300 
L 2325 4250 
L 2125 4450 
L 4825 4450 
L 5050 4700 
L 5450 4350 
L 5200 4175 
z
M 1225 3250 
Q 725 2525 275 2025 
L 200 2075 
Q 675 2750 1087 3625 
Q 1500 4500 1650 5175 
L 2175 4850 
L 1975 4725 
Q 1650 4025 1350 3450 
L 1725 3250 
L 1575 3100 
L 1575 325 
Q 1575 75 1600 -325 
L 1200 -600 
Q 1225 100 1225 600 
L 1225 3250 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4ec5"/>
      <use xlink:href="#SimSun-4c" x="100"/>
      <use xlink:href="#SimSun-53" x="150"/>
      <use xlink:href="#SimSun-54" x="200"/>
      <use xlink:href="#SimSun-4d" x="250"/>
     </g>
    </g>
    <g id="line2d_36">
     <path d="M 576.059375 423.538359 
L 586.559375 423.538359 
L 597.059375 423.538359 
" style="fill: none; stroke-dasharray: 12.8,3.2,2,3.2; stroke-dashoffset: 0; stroke: #ffa500; stroke-width: 2"/>
    </g>
    <g id="text_20">
     <!-- LSTM+移动平均 -->
     <g transform="translate(605.459375 427.213359)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-79fb" d="M 4000 5150 
L 4450 4825 
Q 4275 4775 3875 4300 
L 5225 4300 
L 5425 4525 
L 5800 4150 
L 5575 4050 
Q 5175 3575 4850 3250 
Q 4525 2925 4050 2600 
Q 3575 2275 2825 2000 
L 2775 2075 
Q 3175 2275 3562 2537 
Q 3950 2800 4375 3200 
Q 4800 3600 5225 4150 
L 3800 4150 
L 3625 4000 
Q 3950 3850 4100 3712 
Q 4250 3575 4250 3475 
Q 4250 3375 4162 3250 
Q 4075 3125 4025 3125 
Q 3975 3125 3925 3300 
Q 3825 3600 3575 3925 
Q 3250 3600 2875 3325 
L 2825 3400 
Q 3250 3825 3537 4262 
Q 3825 4700 4000 5150 
z
M 4525 2775 
L 4950 2400 
Q 4650 2275 4275 1900 
L 5475 1900 
L 5675 2125 
L 6050 1750 
L 5850 1650 
Q 5600 1275 5175 787 
Q 4750 300 4012 -62 
Q 3275 -425 2075 -575 
L 2075 -500 
Q 2875 -325 3462 -62 
Q 4050 200 4537 600 
Q 5025 1000 5475 1750 
L 4175 1750 
L 3700 1375 
Q 4075 1150 4162 1012 
Q 4250 875 4250 775 
Q 4250 650 4150 537 
Q 4050 425 4025 425 
Q 3950 425 3925 650 
Q 3850 975 3625 1300 
Q 3025 875 2625 675 
L 2575 750 
Q 3025 1050 3625 1650 
Q 4225 2250 4525 2775 
z
M 475 4375 
Q 1075 4475 1625 4662 
Q 2175 4850 2400 5025 
L 2750 4600 
Q 2375 4550 1875 4475 
L 1875 3225 
L 2275 3225 
L 2550 3500 
L 2950 3075 
L 1875 3075 
L 1875 2525 
Q 2300 2350 2500 2187 
Q 2700 2025 2700 1875 
Q 2700 1750 2637 1625 
Q 2575 1500 2525 1500 
Q 2450 1500 2375 1700 
Q 2250 2000 1875 2400 
Q 1875 250 1900 -400 
L 1500 -600 
Q 1525 350 1525 2200 
Q 1000 1125 350 500 
L 300 550 
Q 725 1175 1037 1850 
Q 1350 2525 1500 3075 
L 1100 3075 
Q 775 3075 500 3000 
L 275 3225 
L 1550 3225 
L 1550 4400 
Q 800 4300 475 4275 
L 475 4375 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-52a8" d="M 5950 3500 
L 5750 3325 
Q 5725 675 5587 137 
Q 5450 -400 4850 -575 
Q 4825 -300 4075 25 
L 4100 150 
Q 4625 25 4925 25 
Q 5075 25 5162 87 
Q 5250 150 5312 737 
Q 5375 1325 5425 3425 
L 4375 3425 
Q 4350 2475 4162 1725 
Q 3975 975 3487 400 
Q 3000 -175 2200 -625 
L 2125 -550 
Q 2925 0 3312 562 
Q 3700 1125 3862 1825 
Q 4025 2525 4050 3425 
L 3600 3425 
L 3300 3375 
L 3100 3575 
L 4050 3575 
Q 4050 4625 4025 5175 
L 4550 4925 
L 4375 4750 
L 4375 3575 
L 5375 3575 
L 5575 3800 
L 5950 3500 
z
M 475 4325 
L 2275 4325 
L 2575 4625 
L 3025 4175 
L 1200 4175 
Q 925 4175 700 4100 
L 475 4325 
z
M 275 3050 
L 2625 3050 
L 2925 3350 
L 3375 2900 
L 1700 2900 
L 2100 2600 
Q 1850 2500 1450 1875 
Q 1025 1250 675 875 
L 2650 1100 
Q 2400 1600 2125 2025 
L 2200 2075 
Q 3075 1275 3125 987 
Q 3175 700 3062 587 
Q 2950 475 2925 475 
Q 2850 475 2825 600 
Q 2775 775 2700 975 
Q 875 650 600 350 
L 325 850 
Q 600 925 1025 1687 
Q 1450 2450 1550 2900 
L 750 2900 
L 475 2850 
L 275 3050 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5e73" d="M 3375 750 
Q 3375 200 3400 -350 
L 2975 -525 
Q 3000 250 3000 875 
L 3000 2325 
L 1050 2325 
Q 725 2325 450 2250 
L 225 2475 
L 3000 2475 
L 3000 4575 
L 1600 4575 
Q 1275 4575 1000 4500 
L 775 4725 
L 4850 4725 
L 5200 5075 
L 5700 4575 
L 3375 4575 
L 3375 2475 
L 5200 2475 
L 5575 2850 
L 6100 2325 
L 3375 2325 
L 3375 750 
z
M 4750 4325 
L 5275 3925 
Q 5075 3925 4725 3475 
Q 4350 3025 3925 2625 
L 3850 2675 
Q 4500 3550 4750 4325 
z
M 1300 4200 
Q 2025 3675 2150 3487 
Q 2275 3300 2275 3150 
Q 2275 3075 2237 2962 
Q 2200 2850 2062 2750 
Q 1925 2650 1800 3100 
Q 1675 3525 1225 4150 
L 1300 4200 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5747" d="M 4075 4875 
L 3825 4725 
Q 3650 4325 3475 3925 
L 5400 3925 
L 5625 4175 
L 5975 3850 
L 5775 3675 
Q 5725 800 5612 337 
Q 5500 -125 5275 -312 
Q 5050 -500 4725 -625 
Q 4625 -250 3800 -25 
L 3800 100 
Q 4575 -50 4887 -12 
Q 5200 25 5287 750 
Q 5375 1475 5425 3775 
L 3425 3775 
Q 2975 2950 2500 2450 
L 2400 2500 
Q 2875 3200 3150 3950 
Q 3425 4700 3475 5200 
L 4075 4875 
z
M 3500 3175 
Q 4075 2850 4237 2637 
Q 4400 2425 4400 2300 
Q 4400 2175 4287 2025 
Q 4175 1875 4125 1875 
Q 4050 1875 3975 2125 
Q 3875 2500 3425 3100 
L 3500 3175 
z
M 4975 1750 
Q 4525 1425 3850 1000 
Q 3150 550 2900 225 
L 2525 700 
Q 2875 800 3462 1075 
Q 4050 1350 4925 1850 
L 4975 1750 
z
M 1700 1100 
L 2775 1500 
L 2825 1375 
Q 1875 925 1325 637 
Q 775 350 650 175 
L 275 675 
Q 950 850 1350 1000 
L 1350 3150 
L 750 3150 
L 525 3100 
L 325 3300 
L 1350 3300 
Q 1350 4700 1325 5175 
L 1925 4875 
L 1700 4675 
L 1700 3300 
L 1975 3300 
L 2250 3575 
L 2650 3150 
L 1700 3150 
L 1700 1100 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-79fb" x="250"/>
      <use xlink:href="#SimSun-52a8" x="350"/>
      <use xlink:href="#SimSun-5e73" x="450"/>
      <use xlink:href="#SimSun-5747" x="550"/>
     </g>
    </g>
    <g id="line2d_37">
     <path d="M 576.059375 438.468047 
L 586.559375 438.468047 
L 597.059375 438.468047 
" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #800080; stroke-width: 2"/>
    </g>
    <g id="text_21">
     <!-- LSTM+指数平滑 -->
     <g transform="translate(605.459375 442.143047)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-6307" d="M 3000 3200 
Q 3000 4875 2975 5175 
L 3575 4900 
L 3350 4700 
L 3350 3800 
Q 3825 3975 4375 4287 
Q 4925 4600 5150 4850 
L 5525 4375 
Q 5325 4375 4825 4175 
Q 4300 3975 3350 3675 
L 3350 3250 
Q 3350 2925 3725 2950 
L 5200 2950 
Q 5400 2975 5450 3175 
Q 5500 3375 5550 4000 
L 5650 4000 
Q 5675 3600 5712 3362 
Q 5750 3125 5975 3050 
Q 5825 2675 5400 2675 
L 3475 2675 
Q 2975 2650 3000 3200 
z
M 5475 1850 
L 5475 225 
Q 5475 25 5500 -375 
L 5125 -525 
L 5125 -50 
L 3400 -50 
L 3400 -400 
L 3025 -575 
Q 3050 100 3050 925 
Q 3050 1750 3025 2300 
L 3400 2075 
L 5075 2075 
L 5250 2325 
L 5675 2025 
L 5475 1850 
z
M 3400 1925 
L 3400 1075 
L 5125 1075 
L 5125 1925 
L 3400 1925 
z
M 3400 925 
L 3400 100 
L 5125 100 
L 5125 925 
L 3400 925 
z
M 1450 3750 
Q 1450 4750 1425 5225 
L 2025 4925 
L 1800 4750 
L 1800 3750 
L 2125 3750 
L 2425 4050 
L 2875 3600 
L 1800 3600 
L 1800 2450 
L 2650 2900 
L 2700 2825 
L 1800 2200 
L 1800 75 
Q 1800 -375 1275 -600 
Q 1275 -300 550 -75 
L 550 25 
Q 1100 -50 1275 -37 
Q 1450 -25 1450 300 
L 1450 2000 
Q 1100 1775 725 1500 
L 650 1350 
L 325 1800 
Q 625 1900 1450 2300 
L 1450 3600 
L 1175 3600 
Q 850 3600 575 3525 
L 350 3750 
L 1450 3750 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6570" d="M 1875 1525 
Q 1675 1200 1500 900 
Q 1825 825 2275 725 
Q 2500 975 2725 1525 
L 1875 1525 
z
M 850 4900 
Q 1325 4600 1425 4450 
Q 1525 4300 1525 4225 
Q 1525 4100 1437 3975 
Q 1350 3850 1300 3850 
Q 1225 3850 1150 4100 
Q 1050 4450 775 4850 
L 850 4900 
z
M 3000 4975 
L 3450 4675 
Q 3250 4625 3100 4425 
Q 2925 4225 2525 3825 
L 2450 3875 
Q 2925 4625 3000 4975 
z
M 1875 5250 
L 2425 5000 
L 2225 4825 
L 2225 3675 
L 2850 3675 
L 3125 3950 
L 3550 3525 
L 2225 3525 
L 2225 3300 
Q 2800 3175 2937 3062 
Q 3075 2950 3075 2775 
Q 3075 2700 3050 2587 
Q 3025 2475 2975 2475 
Q 2925 2475 2800 2650 
Q 2600 2925 2225 3175 
L 2225 2400 
L 1925 2200 
L 2275 2050 
Q 2150 2000 1950 1675 
L 2725 1675 
L 2900 1900 
L 3275 1575 
L 3050 1450 
Q 2800 875 2600 650 
Q 3000 550 3137 437 
Q 3275 325 3275 150 
Q 3275 -25 3200 -25 
Q 3125 -25 3025 75 
Q 2750 275 2400 425 
Q 1525 -275 325 -500 
L 300 -425 
Q 1450 -50 2100 550 
Q 1600 725 1125 800 
Q 1225 950 1525 1525 
L 1200 1525 
Q 875 1525 600 1450 
L 375 1675 
L 1575 1675 
Q 1675 1925 1775 2275 
L 1900 2225 
L 1900 3250 
Q 1225 2475 350 2050 
L 300 2125 
Q 1225 2775 1700 3525 
L 1250 3525 
Q 925 3525 650 3450 
L 425 3675 
L 1900 3675 
Q 1900 4575 1875 5250 
z
M 3925 3525 
Q 4050 2075 4525 1100 
Q 4925 1950 5025 3525 
L 3925 3525 
z
M 4050 5250 
L 4625 4925 
Q 4425 4850 4325 4625 
Q 4225 4400 3950 3675 
L 5375 3675 
L 5675 3975 
L 6125 3525 
L 5400 3525 
Q 5250 1675 4750 775 
Q 5325 75 6100 -100 
L 6100 -200 
Q 5625 -275 5600 -500 
Q 4950 -50 4525 525 
Q 3700 -275 2550 -650 
L 2500 -575 
Q 3650 -50 4325 800 
Q 3850 1975 3825 3375 
Q 3575 2725 3100 2150 
L 3025 2200 
Q 3625 3200 4050 5250 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6ed1" d="M 2675 3225 
Q 2675 4575 2650 5025 
L 3025 4800 
L 4700 4800 
L 4925 5025 
L 5275 4675 
L 5050 4525 
L 5050 3225 
L 5450 3225 
L 5675 3475 
L 6075 3075 
Q 5850 3025 5687 2887 
Q 5525 2750 5350 2525 
L 5275 2550 
L 5500 3075 
L 2275 3075 
Q 2275 2825 2225 2662 
Q 2175 2500 2012 2462 
Q 1850 2425 1775 2537 
Q 1700 2650 1900 2825 
Q 2075 2975 2200 3500 
L 2275 3500 
L 2275 3225 
L 2675 3225 
z
M 3000 2375 
L 3000 1750 
L 4725 1750 
L 4725 2375 
L 3000 2375 
z
M 3000 1600 
L 3000 950 
L 4725 950 
L 4725 1600 
L 3000 1600 
z
M 2650 -575 
Q 2675 150 2675 1025 
Q 2675 1900 2650 2725 
L 3000 2525 
L 4700 2525 
L 4925 2750 
L 5275 2400 
L 5050 2250 
L 5050 50 
Q 5050 -225 4937 -362 
Q 4825 -500 4575 -600 
Q 4425 -275 3850 -125 
L 3850 -25 
Q 4500 -100 4612 -75 
Q 4725 -50 4725 125 
L 4725 800 
L 3000 800 
L 3000 -400 
L 2650 -575 
z
M 3000 4650 
L 3000 4075 
L 3750 4075 
L 3975 4300 
L 4275 4000 
L 4075 3875 
L 4075 3225 
L 4725 3225 
L 4725 4650 
L 3000 4650 
z
M 3000 3925 
L 3000 3225 
L 3775 3225 
L 3775 3925 
L 3000 3925 
z
M 1925 3975 
Q 1375 1475 1300 1175 
Q 1200 850 1225 400 
Q 1225 -50 1225 -187 
Q 1225 -325 1100 -325 
Q 1000 -325 862 -275 
Q 725 -225 725 -50 
Q 725 75 800 325 
Q 875 575 875 725 
Q 875 900 762 1000 
Q 650 1100 275 1200 
L 275 1300 
Q 725 1250 837 1262 
Q 950 1275 1087 1487 
Q 1225 1700 1825 4000 
L 1925 3975 
z
M 300 3775 
Q 1075 3375 1087 3100 
Q 1100 2825 962 2725 
Q 825 2625 725 2975 
Q 600 3325 250 3700 
L 300 3775 
z
M 875 5100 
Q 1575 4750 1637 4575 
Q 1700 4400 1700 4325 
Q 1700 4200 1612 4100 
Q 1525 4000 1475 4000 
Q 1400 4000 1325 4250 
Q 1200 4575 825 5025 
L 875 5100 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-6307" x="250"/>
      <use xlink:href="#SimSun-6570" x="350"/>
      <use xlink:href="#SimSun-5e73" x="450"/>
      <use xlink:href="#SimSun-6ed1" x="550"/>
     </g>
    </g>
    <g id="line2d_38">
     <path d="M 576.059375 453.397734 
L 586.559375 453.397734 
L 597.059375 453.397734 
" style="fill: none; stroke: #a52a2a; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_22">
     <!-- LSTM+加权平均 -->
     <g transform="translate(605.459375 457.072734)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-52a0" d="M 1500 3800 
Q 1500 4700 1475 5250 
L 2075 4975 
L 1850 4800 
L 1850 3800 
L 2750 3800 
L 2950 4050 
L 3325 3725 
L 3100 3550 
Q 3025 650 2825 200 
Q 2625 -250 2150 -350 
Q 2175 -25 1450 250 
L 1475 375 
Q 2050 225 2225 225 
Q 2325 225 2437 287 
Q 2550 350 2637 862 
Q 2725 1375 2775 3650 
L 1850 3650 
Q 1825 2825 1762 2212 
Q 1700 1600 1400 900 
Q 1100 200 325 -525 
L 250 -450 
Q 825 225 1087 875 
Q 1350 1525 1425 2200 
Q 1500 2875 1500 3650 
L 825 3650 
L 525 3600 
L 325 3800 
L 1500 3800 
z
M 5725 3700 
L 5725 975 
Q 5725 375 5750 -175 
L 5400 -325 
L 5400 350 
L 4075 350 
L 4075 -300 
L 3725 -475 
Q 3750 300 3750 1850 
Q 3750 3400 3725 4175 
L 4100 3925 
L 5350 3925 
L 5575 4150 
L 5900 3825 
L 5725 3700 
z
M 4075 3775 
L 4075 500 
L 5400 500 
L 5400 3775 
L 4075 3775 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6743" d="M 3300 4250 
Q 3500 2475 4125 1500 
Q 4725 2525 5025 4250 
L 3300 4250 
z
M 2650 4400 
L 5000 4400 
L 5175 4675 
L 5625 4325 
L 5400 4150 
Q 5025 2225 4325 1150 
Q 5225 150 6150 -25 
L 6150 -125 
Q 5700 -175 5600 -450 
Q 4650 125 4100 925 
Q 3125 -100 2000 -575 
L 1950 -500 
Q 3175 225 3900 1225 
Q 3325 2375 3175 4250 
L 2875 4175 
L 2650 4400 
z
M 1825 2725 
Q 1825 25 1850 -475 
L 1450 -650 
Q 1475 525 1475 2425 
Q 1050 1375 300 575 
L 250 650 
Q 1075 1850 1450 3550 
L 650 3550 
L 425 3500 
L 225 3700 
L 1475 3700 
Q 1475 4475 1450 5225 
L 2050 4975 
L 1825 4775 
L 1825 3700 
L 2225 3700 
L 2525 4000 
L 2975 3550 
L 1825 3550 
L 1825 2875 
Q 2475 2600 2662 2425 
Q 2850 2250 2850 2075 
Q 2850 1975 2787 1812 
Q 2725 1650 2675 1650 
Q 2600 1650 2475 1925 
Q 2275 2325 1825 2725 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-52a0" x="250"/>
      <use xlink:href="#SimSun-6743" x="350"/>
      <use xlink:href="#SimSun-5e73" x="450"/>
      <use xlink:href="#SimSun-5747" x="550"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p73b1b5e053">
   <rect x="50.309375" y="24.35625" width="669.6" height="443.52"/>
  </clipPath>
 </defs>
</svg>
