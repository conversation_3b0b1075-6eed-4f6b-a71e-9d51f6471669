<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1070.900391pt" height="710.015156pt" viewBox="0 0 1070.900391 710.015156" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-08T10:39:03.676458</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 710.015156 
L 1070.900391 710.015156 
L 1070.900391 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 48.756641 319.143906 
L 1063.700391 319.143906 
L 1063.700391 23.191406 
L 48.756641 23.191406 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 94.890447 319.143906 
L 94.890447 23.191406 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m85c669cb6f" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m85c669cb6f" x="94.890447" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g transform="translate(92.265447 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-30" d="M 1600 4225 
Q 1250 4225 1012 3687 
Q 775 3150 775 2250 
Q 775 1300 1012 775 
Q 1250 250 1600 250 
Q 1975 250 2187 775 
Q 2400 1300 2400 2250 
Q 2400 3150 2200 3687 
Q 2000 4225 1600 4225 
z
M 1600 50 
Q 1050 50 675 625 
Q 300 1200 300 2250 
Q 300 3225 662 3825 
Q 1025 4425 1600 4425 
Q 2150 4425 2512 3850 
Q 2875 3275 2875 2250 
Q 2875 1225 2512 637 
Q 2150 50 1600 50 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 283.1917 319.143906 
L 283.1917 23.191406 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m85c669cb6f" x="283.1917" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 10 -->
      <g transform="translate(277.9417 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-31" d="M 1825 4450 
L 1825 650 
Q 1825 450 1950 350 
Q 2075 250 2300 250 
L 2550 250 
L 2550 100 
L 725 100 
L 725 250 
L 950 250 
Q 1200 250 1312 350 
Q 1425 450 1425 650 
L 1425 3675 
Q 1425 3775 1362 3837 
Q 1300 3900 1175 3900 
L 725 3900 
L 725 4050 
L 950 4050 
Q 1250 4050 1437 4150 
Q 1625 4250 1725 4450 
L 1825 4450 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 471.492952 319.143906 
L 471.492952 23.191406 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m85c669cb6f" x="471.492952" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 20 -->
      <g transform="translate(466.242952 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-32" d="M 2325 3325 
Q 2325 3775 2125 4012 
Q 1925 4250 1525 4250 
Q 1225 4250 1012 4087 
Q 800 3925 800 3675 
Q 800 3525 900 3425 
Q 975 3325 975 3225 
Q 975 3100 912 3037 
Q 850 2975 725 2975 
Q 575 2975 487 3062 
Q 400 3150 400 3350 
Q 400 3875 775 4150 
Q 1150 4425 1575 4425 
Q 2175 4425 2462 4125 
Q 2750 3825 2750 3375 
Q 2750 3075 2612 2775 
Q 2475 2475 2175 2200 
Q 1450 1500 1062 1062 
Q 675 625 600 475 
L 2075 475 
Q 2300 475 2450 650 
Q 2600 825 2650 1175 
L 2800 1175 
L 2650 100 
L 325 100 
L 325 425 
Q 450 650 737 1000 
Q 1025 1350 1500 1825 
Q 1925 2250 2125 2625 
Q 2325 3000 2325 3325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-32"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 659.794204 319.143906 
L 659.794204 23.191406 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m85c669cb6f" x="659.794204" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 30 -->
      <g transform="translate(654.544204 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-33" d="M 2800 1250 
Q 2800 775 2450 412 
Q 2100 50 1475 50 
Q 1025 50 700 300 
Q 375 550 375 875 
Q 375 1025 475 1137 
Q 575 1250 675 1250 
Q 825 1250 887 1137 
Q 950 1025 950 950 
Q 950 825 900 750 
Q 850 650 850 575 
Q 850 425 1037 325 
Q 1225 225 1450 225 
Q 1900 225 2125 487 
Q 2350 750 2350 1300 
Q 2350 1750 2112 2025 
Q 1875 2300 1225 2300 
L 1225 2475 
Q 1725 2475 1962 2712 
Q 2200 2950 2200 3375 
Q 2200 3725 2012 3987 
Q 1825 4250 1425 4250 
Q 1250 4250 1050 4162 
Q 850 4075 850 3875 
Q 850 3675 900 3625 
Q 950 3575 950 3500 
Q 950 3375 900 3300 
Q 850 3225 725 3225 
Q 625 3225 537 3300 
Q 450 3375 450 3575 
Q 450 3950 775 4187 
Q 1100 4425 1525 4425 
Q 2025 4425 2325 4112 
Q 2625 3800 2625 3450 
Q 2625 3075 2437 2812 
Q 2250 2550 1850 2425 
Q 2400 2225 2600 1900 
Q 2800 1575 2800 1250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-33"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 848.095457 319.143906 
L 848.095457 23.191406 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m85c669cb6f" x="848.095457" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 40 -->
      <g transform="translate(842.845457 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-34" d="M 2300 575 
Q 2300 400 2400 325 
Q 2500 250 2675 250 
L 2900 250 
L 2900 100 
L 1250 100 
L 1250 250 
L 1525 250 
Q 1725 250 1812 325 
Q 1900 400 1900 575 
L 1900 1400 
L 225 1400 
L 225 1525 
L 2050 4425 
L 2300 4425 
L 2300 1550 
L 2975 1550 
L 2975 1400 
L 2300 1400 
L 2300 575 
z
M 1875 3800 
L 450 1550 
L 1900 1550 
L 1900 3800 
L 1875 3800 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-34"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 1036.396709 319.143906 
L 1036.396709 23.191406 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m85c669cb6f" x="1036.396709" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 50 -->
      <g transform="translate(1031.146709 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-35" d="M 1725 2850 
Q 2200 2850 2500 2487 
Q 2800 2125 2800 1525 
Q 2800 850 2487 450 
Q 2175 50 1525 50 
Q 1075 50 725 312 
Q 375 575 375 950 
Q 375 1100 462 1212 
Q 550 1325 700 1325 
Q 850 1325 900 1225 
Q 950 1125 950 1050 
Q 950 900 875 825 
Q 800 725 800 625 
Q 800 425 1037 325 
Q 1275 225 1550 225 
Q 1950 225 2162 550 
Q 2375 875 2375 1475 
Q 2375 1975 2187 2287 
Q 2000 2600 1625 2600 
Q 1350 2600 1150 2500 
Q 950 2400 775 2075 
L 550 2100 
L 675 4375 
L 2725 4375 
L 2650 4000 
L 850 4000 
L 750 2350 
Q 1000 2700 1237 2775 
Q 1475 2850 1725 2850 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-35"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="text_7">
     <!-- 时间步 -->
     <g transform="translate(540.478516 347.370469)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-65f6" d="M 4500 3600 
Q 4500 4575 4475 5225 
L 5100 4900 
L 4850 4725 
L 4850 3600 
L 5225 3600 
L 5550 3925 
L 6025 3450 
L 4850 3450 
L 4850 100 
Q 4850 -375 4300 -575 
Q 4275 -200 3475 -25 
L 3475 100 
Q 4150 0 4325 25 
Q 4500 50 4500 325 
L 4500 3450 
L 3200 3450 
Q 2875 3450 2600 3375 
L 2375 3600 
L 4500 3600 
z
M 2875 2825 
Q 3525 2375 3637 2200 
Q 3750 2025 3750 1925 
Q 3750 1800 3637 1637 
Q 3525 1475 3475 1475 
Q 3400 1475 3325 1775 
Q 3200 2225 2800 2750 
L 2875 2825 
z
M 925 4150 
L 925 2650 
L 1950 2650 
L 1950 4150 
L 925 4150 
z
M 925 2500 
L 925 900 
L 1950 900 
L 1950 2500 
L 925 2500 
z
M 2300 2000 
Q 2300 800 2325 450 
L 1950 250 
L 1950 750 
L 925 750 
L 925 225 
L 550 25 
Q 575 825 575 2425 
Q 575 4025 550 4500 
L 950 4300 
L 1900 4300 
L 2100 4550 
L 2500 4225 
L 2300 4050 
L 2300 2000 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-95f4" d="M 2150 525 
Q 2175 1325 2175 2112 
Q 2175 2900 2150 3675 
L 2525 3450 
L 3750 3450 
L 4000 3700 
L 4325 3375 
L 4125 3200 
Q 4125 1550 4150 775 
L 3800 575 
L 3800 975 
L 2500 975 
L 2500 700 
L 2150 525 
z
M 2500 3300 
L 2500 2325 
L 3800 2325 
L 3800 3300 
L 2500 3300 
z
M 2500 2175 
L 2500 1125 
L 3800 1125 
L 3800 2175 
L 2500 2175 
z
M 1275 5200 
Q 1850 4825 1950 4675 
Q 2050 4525 2050 4425 
Q 2050 4275 1950 4137 
Q 1850 4000 1825 4000 
Q 1750 4000 1675 4275 
Q 1550 4650 1200 5125 
L 1275 5200 
z
M 1125 425 
Q 1125 25 1150 -375 
L 750 -525 
Q 775 -150 775 1950 
Q 775 4050 750 4425 
L 1350 4125 
L 1125 3975 
L 1125 425 
z
M 4225 125 
Q 5025 0 5125 37 
Q 5225 75 5225 250 
L 5225 4450 
L 3225 4450 
Q 2900 4450 2625 4375 
L 2400 4600 
L 5175 4600 
L 5425 4850 
L 5775 4500 
L 5575 4350 
L 5575 225 
Q 5575 -100 5462 -250 
Q 5350 -400 5000 -550 
Q 4800 -125 4225 25 
L 4225 125 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6b65" d="M 3125 2975 
Q 3125 4700 3100 5275 
L 3700 5000 
L 3475 4825 
L 3475 4200 
L 4800 4200 
L 5100 4500 
L 5550 4050 
L 3475 4050 
L 3475 2975 
L 5175 2975 
L 5550 3350 
L 6075 2825 
L 1150 2825 
Q 825 2825 550 2750 
L 325 2975 
L 1500 2975 
Q 1500 4225 1475 4650 
L 2075 4375 
L 1850 4200 
L 1850 2975 
L 3125 2975 
z
M 3100 650 
Q 3125 950 3125 1737 
Q 3125 2525 3100 2775 
L 3675 2500 
L 3475 2325 
Q 3475 1175 3500 800 
L 3100 650 
z
M 1850 2300 
L 2350 1925 
L 2075 1825 
Q 1275 750 550 250 
L 500 325 
Q 975 800 1350 1387 
Q 1725 1975 1850 2300 
z
M 5250 2275 
L 5700 1825 
L 5475 1775 
Q 3950 325 2875 -62 
Q 1800 -450 375 -650 
L 375 -550 
Q 1125 -425 2075 -112 
Q 3025 200 3862 837 
Q 4700 1475 5250 2275 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-65f6"/>
      <use xlink:href="#SimSun-95f4" x="100"/>
      <use xlink:href="#SimSun-6b65" x="200"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_13">
      <path d="M 48.756641 296.381495 
L 1063.700391 296.381495 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_14">
      <defs>
       <path id="mcdacb2ce2e" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="296.381495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- -0.2 -->
      <g transform="translate(20.756641 300.134424)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-2d" d="M 200 2225 
L 200 2450 
L 2975 2450 
L 2975 2225 
L 200 2225 
z
" transform="scale(0.015625)"/>
        <path id="SimSun-2e" d="M 800 25 
Q 650 25 537 125 
Q 425 225 425 400 
Q 425 575 537 675 
Q 650 775 800 775 
Q 950 775 1062 662 
Q 1175 550 1175 400 
Q 1175 225 1062 125 
Q 950 25 800 25 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_15">
      <path d="M 48.756641 252.610583 
L 1063.700391 252.610583 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="252.610583" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- -0.1 -->
      <g transform="translate(20.756641 256.363513)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-31" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_17">
      <path d="M 48.756641 208.839672 
L 1063.700391 208.839672 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="208.839672" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 0.0 -->
      <g transform="translate(26.006641 212.592602)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_19">
      <path d="M 48.756641 165.068761 
L 1063.700391 165.068761 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="165.068761" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.1 -->
      <g transform="translate(26.006641 168.821691)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-31" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_21">
      <path d="M 48.756641 121.29785 
L 1063.700391 121.29785 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="121.29785" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.2 -->
      <g transform="translate(26.006641 125.05078)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_23">
      <path d="M 48.756641 77.526939 
L 1063.700391 77.526939 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="77.526939" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.3 -->
      <g transform="translate(26.006641 81.279869)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-33" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_25">
      <path d="M 48.756641 33.756028 
L 1063.700391 33.756028 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="33.756028" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0.4 -->
      <g transform="translate(26.006641 37.508957)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_15">
     <!-- 位移 (μm) -->
     <g transform="translate(15.403125 197.417656)rotate(-90)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-4f4d" d="M 1250 -600 
Q 1275 150 1275 3100 
Q 750 2225 325 1775 
L 250 1825 
Q 725 2575 1112 3450 
Q 1500 4325 1700 5225 
L 2275 4925 
L 2050 4800 
Q 1650 3900 1475 3500 
L 1800 3275 
L 1600 3125 
Q 1600 125 1625 -400 
L 1250 -600 
z
M 3300 5125 
L 3375 5175 
Q 4100 4625 4112 4375 
Q 4125 4125 4000 4025 
Q 3875 3925 3850 3925 
Q 3725 3925 3700 4200 
Q 3625 4575 3300 5125 
z
M 2025 3725 
L 5050 3725 
L 5400 4075 
L 5900 3575 
L 2825 3575 
Q 2525 3575 2250 3500 
L 2025 3725 
z
M 2600 3000 
L 2675 3050 
Q 3300 2050 3400 1712 
Q 3500 1375 3500 1200 
Q 3500 950 3362 800 
Q 3225 650 3175 650 
Q 3100 650 3100 825 
Q 3075 1125 2975 1675 
Q 2875 2225 2600 3000 
z
M 4725 3325 
L 5325 3000 
Q 5100 2875 4875 2125 
Q 4650 1375 4175 -25 
L 5300 -25 
L 5650 325 
L 6150 -175 
L 2700 -175 
Q 2375 -175 2100 -250 
L 1875 -25 
L 4000 -25 
Q 4300 1000 4500 1962 
Q 4700 2925 4725 3325 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-79fb" d="M 4000 5150 
L 4450 4825 
Q 4275 4775 3875 4300 
L 5225 4300 
L 5425 4525 
L 5800 4150 
L 5575 4050 
Q 5175 3575 4850 3250 
Q 4525 2925 4050 2600 
Q 3575 2275 2825 2000 
L 2775 2075 
Q 3175 2275 3562 2537 
Q 3950 2800 4375 3200 
Q 4800 3600 5225 4150 
L 3800 4150 
L 3625 4000 
Q 3950 3850 4100 3712 
Q 4250 3575 4250 3475 
Q 4250 3375 4162 3250 
Q 4075 3125 4025 3125 
Q 3975 3125 3925 3300 
Q 3825 3600 3575 3925 
Q 3250 3600 2875 3325 
L 2825 3400 
Q 3250 3825 3537 4262 
Q 3825 4700 4000 5150 
z
M 4525 2775 
L 4950 2400 
Q 4650 2275 4275 1900 
L 5475 1900 
L 5675 2125 
L 6050 1750 
L 5850 1650 
Q 5600 1275 5175 787 
Q 4750 300 4012 -62 
Q 3275 -425 2075 -575 
L 2075 -500 
Q 2875 -325 3462 -62 
Q 4050 200 4537 600 
Q 5025 1000 5475 1750 
L 4175 1750 
L 3700 1375 
Q 4075 1150 4162 1012 
Q 4250 875 4250 775 
Q 4250 650 4150 537 
Q 4050 425 4025 425 
Q 3950 425 3925 650 
Q 3850 975 3625 1300 
Q 3025 875 2625 675 
L 2575 750 
Q 3025 1050 3625 1650 
Q 4225 2250 4525 2775 
z
M 475 4375 
Q 1075 4475 1625 4662 
Q 2175 4850 2400 5025 
L 2750 4600 
Q 2375 4550 1875 4475 
L 1875 3225 
L 2275 3225 
L 2550 3500 
L 2950 3075 
L 1875 3075 
L 1875 2525 
Q 2300 2350 2500 2187 
Q 2700 2025 2700 1875 
Q 2700 1750 2637 1625 
Q 2575 1500 2525 1500 
Q 2450 1500 2375 1700 
Q 2250 2000 1875 2400 
Q 1875 250 1900 -400 
L 1500 -600 
Q 1525 350 1525 2200 
Q 1000 1125 350 500 
L 300 550 
Q 725 1175 1037 1850 
Q 1350 2525 1500 3075 
L 1100 3075 
Q 775 3075 500 3000 
L 275 3225 
L 1550 3225 
L 1550 4400 
Q 800 4300 475 4275 
L 475 4375 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-20" d="M 0 0 
Q 0 0 0 0 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-28" d="M 2900 5025 
Q 2325 4425 2037 3737 
Q 1750 3050 1750 2300 
Q 1750 1525 2037 850 
Q 2325 175 2900 -425 
L 2775 -550 
Q 2075 75 1725 787 
Q 1375 1500 1375 2300 
Q 1375 3075 1725 3787 
Q 2075 4500 2775 5150 
L 2900 5025 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-3bc" d="M 2575 900 
L 2575 250 
Q 2575 50 2950 75 
L 2950 -75 
L 1825 -75 
L 1825 75 
Q 2200 50 2200 250 
L 2200 3000 
Q 2225 3175 1950 3175 
L 1950 3300 
L 2575 3300 
L 2575 1300 
Q 2650 950 3112 950 
Q 3575 950 3850 1425 
L 3850 3025 
Q 3850 3175 3650 3175 
L 3650 3300 
L 4225 3300 
L 4225 1375 
Q 4200 1000 4325 1025 
Q 4450 1050 4575 1150 
Q 4700 1250 4737 1225 
Q 4775 1200 4587 912 
Q 4400 625 4125 687 
Q 3850 750 3850 1075 
Q 3400 600 3037 650 
Q 2675 700 2575 900 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6d" d="M 175 2900 
Q 300 2900 425 2925 
Q 550 2950 625 3000 
L 675 3000 
L 675 2575 
Q 800 2775 950 2862 
Q 1100 2950 1275 2950 
Q 1500 2950 1625 2862 
Q 1750 2775 1775 2575 
Q 1900 2775 2062 2862 
Q 2225 2950 2400 2950 
Q 2700 2950 2812 2775 
Q 2925 2600 2925 2325 
L 2925 425 
Q 2925 325 2975 287 
Q 3025 250 3125 250 
L 3175 250 
L 3175 100 
L 2325 100 
L 2325 250 
L 2375 250 
Q 2475 250 2525 287 
Q 2575 325 2575 425 
L 2575 2300 
Q 2575 2575 2512 2662 
Q 2450 2750 2325 2750 
Q 2150 2750 2012 2625 
Q 1875 2500 1800 2275 
L 1800 425 
Q 1800 325 1850 287 
Q 1900 250 2000 250 
L 2050 250 
L 2050 100 
L 1200 100 
L 1200 250 
L 1250 250 
Q 1350 250 1400 287 
Q 1450 325 1450 425 
L 1450 2300 
Q 1450 2525 1400 2637 
Q 1350 2750 1200 2750 
Q 1025 2750 887 2625 
Q 750 2500 675 2250 
L 675 425 
Q 675 325 725 287 
Q 775 250 875 250 
L 925 250 
L 925 100 
L 75 100 
L 75 250 
L 125 250 
Q 225 250 275 287 
Q 325 325 325 425 
L 325 2600 
Q 325 2675 300 2712 
Q 275 2750 200 2750 
L 100 2750 
L 100 2900 
L 175 2900 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-29" d="M 275 5025 
L 400 5150 
Q 1100 4500 1450 3787 
Q 1800 3075 1800 2300 
Q 1800 1500 1450 787 
Q 1100 75 400 -550 
L 275 -425 
Q 850 175 1137 850 
Q 1425 1525 1425 2300 
Q 1425 3050 1137 3737 
Q 850 4425 275 5025 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4f4d"/>
      <use xlink:href="#SimSun-79fb" x="100"/>
      <use xlink:href="#SimSun-20" x="200"/>
      <use xlink:href="#SimSun-28" x="250"/>
      <use xlink:href="#SimSun-3bc" x="300"/>
      <use xlink:href="#SimSun-6d" x="400"/>
      <use xlink:href="#SimSun-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="line2d_27">
    <path d="M 94.890447 305.69152 
L 113.720573 290.181413 
L 132.550698 272.856178 
L 151.380823 254.996647 
L 170.210948 239.274478 
L 189.041074 226.268037 
L 207.871199 216.541796 
L 226.701324 208.761365 
L 245.531449 201.907374 
L 264.361575 194.438721 
L 283.1917 185.584679 
L 302.021825 175.050389 
L 320.85195 162.239121 
L 339.682075 147.951455 
L 358.512201 132.509942 
L 377.342326 117.940052 
L 396.172451 105.184069 
L 415.002576 95.673564 
L 433.832702 88.876034 
L 452.662827 83.930705 
L 471.492952 79.126883 
L 490.323077 73.693849 
L 509.153203 67.423883 
L 527.983328 61.34396 
L 546.813453 56.055562 
L 565.643578 51.998001 
L 584.473703 48.526177 
L 603.303829 45.127293 
L 622.133954 41.499074 
L 640.964079 38.384139 
L 659.794204 36.643793 
L 678.62433 36.927784 
L 697.454455 38.945346 
L 716.28458 41.640602 
L 735.114705 44.201743 
L 753.944831 46.424954 
L 772.774956 49.190177 
L 791.605081 53.347154 
L 810.435206 59.314242 
L 829.265331 66.006931 
L 848.095457 72.298255 
L 866.925582 77.229889 
L 885.755707 81.878076 
L 904.585832 88.304757 
L 923.415958 99.177401 
L 942.246083 114.8274 
L 961.076208 134.113522 
L 979.906333 152.555846 
L 998.736459 166.859097 
L 1017.566584 174.28311 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_28">
    <path d="M 94.890447 305.69152 
L 113.720573 286.294256 
L 132.550698 267.299508 
L 151.380823 251.785749 
L 170.210948 230.553318 
L 189.041074 204.800207 
L 207.871199 188.558001 
L 226.701324 202.488867 
L 245.531449 198.476192 
L 264.361575 171.799778 
L 283.1917 171.933308 
L 302.021825 187.382531 
L 320.85195 162.343711 
L 339.682075 173.891319 
L 358.512201 133.122804 
L 377.342326 157.271672 
L 396.172451 99.012993 
L 415.002576 76.934537 
L 433.832702 115.542484 
L 452.662827 89.827505 
L 471.492952 83.829278 
L 490.323077 85.8911 
L 509.153203 82.170241 
L 527.983328 122.167692 
L 546.813453 70.540516 
L 565.643578 102.942782 
L 584.473703 73.01776 
L 603.303829 57.112326 
L 622.133954 52.323149 
L 640.964079 49.476 
L 659.794204 51.953002 
L 678.62433 52.096823 
L 697.454455 54.220598 
L 716.28458 51.193459 
L 735.114705 114.56165 
L 753.944831 64.966887 
L 772.774956 97.65996 
L 791.605081 86.663254 
L 810.435206 58.509362 
L 829.265331 79.921578 
L 848.095457 75.102011 
L 866.925582 91.238802 
L 885.755707 106.89346 
L 904.585832 99.711657 
L 923.415958 121.295392 
L 942.246083 124.31354 
L 961.076208 138.91588 
L 979.906333 128.847454 
L 998.736459 176.684668 
L 1017.566584 155.42085 
" clip-path="url(#pea463a64b3)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
   </g>
   <g id="patch_3">
    <path d="M 48.756641 319.143906 
L 48.756641 23.191406 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 1063.700391 319.143906 
L 1063.700391 23.191406 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 48.756641 319.143906 
L 1063.700391 319.143906 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 48.756641 23.191406 
L 1063.700391 23.191406 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_16">
    <!-- 振动数据预测结果 -->
    <g transform="translate(505.828516 17.191406)scale(0.126 -0.126)">
     <defs>
      <path id="SimSun-632f" d="M 400 100 
Q 950 25 1125 25 
Q 1300 25 1300 325 
L 1300 2000 
Q 1025 1875 650 1650 
L 525 1475 
L 225 1925 
Q 675 2050 1300 2275 
L 1300 3550 
L 700 3550 
L 475 3500 
L 275 3700 
L 1300 3700 
Q 1300 4700 1275 5200 
L 1875 4925 
L 1650 4750 
L 1650 3700 
L 1925 3700 
L 2200 3975 
L 2625 3550 
L 1650 3550 
L 1650 2400 
L 2475 2725 
L 2500 2650 
L 1650 2175 
L 1650 150 
Q 1675 -325 1125 -550 
Q 1125 -225 400 0 
L 400 100 
z
M 3750 150 
L 4525 550 
L 4575 475 
Q 3775 -125 3625 -475 
L 3325 -75 
Q 3450 100 3425 350 
L 3425 2450 
L 3075 2450 
Q 2975 350 1875 -625 
L 1800 -575 
Q 2250 0 2487 837 
Q 2725 1675 2725 2937 
Q 2725 4200 2700 4900 
L 3075 4650 
L 5000 4650 
L 5350 5000 
L 5850 4500 
L 3075 4500 
L 3075 2600 
L 5150 2600 
L 5500 2950 
L 6000 2450 
L 4275 2450 
Q 4425 1850 4675 1375 
Q 5300 1825 5550 2150 
L 5900 1725 
Q 5550 1725 4750 1250 
Q 5050 675 5437 375 
Q 5825 75 6125 25 
L 6125 -75 
Q 5725 -100 5550 -375 
Q 4400 700 4150 2450 
L 3750 2450 
L 3750 150 
z
M 3300 3625 
L 4925 3625 
L 5225 3925 
L 5675 3475 
L 4050 3475 
Q 3725 3475 3525 3400 
L 3300 3625 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-52a8" d="M 5950 3500 
L 5750 3325 
Q 5725 675 5587 137 
Q 5450 -400 4850 -575 
Q 4825 -300 4075 25 
L 4100 150 
Q 4625 25 4925 25 
Q 5075 25 5162 87 
Q 5250 150 5312 737 
Q 5375 1325 5425 3425 
L 4375 3425 
Q 4350 2475 4162 1725 
Q 3975 975 3487 400 
Q 3000 -175 2200 -625 
L 2125 -550 
Q 2925 0 3312 562 
Q 3700 1125 3862 1825 
Q 4025 2525 4050 3425 
L 3600 3425 
L 3300 3375 
L 3100 3575 
L 4050 3575 
Q 4050 4625 4025 5175 
L 4550 4925 
L 4375 4750 
L 4375 3575 
L 5375 3575 
L 5575 3800 
L 5950 3500 
z
M 475 4325 
L 2275 4325 
L 2575 4625 
L 3025 4175 
L 1200 4175 
Q 925 4175 700 4100 
L 475 4325 
z
M 275 3050 
L 2625 3050 
L 2925 3350 
L 3375 2900 
L 1700 2900 
L 2100 2600 
Q 1850 2500 1450 1875 
Q 1025 1250 675 875 
L 2650 1100 
Q 2400 1600 2125 2025 
L 2200 2075 
Q 3075 1275 3125 987 
Q 3175 700 3062 587 
Q 2950 475 2925 475 
Q 2850 475 2825 600 
Q 2775 775 2700 975 
Q 875 650 600 350 
L 325 850 
Q 600 925 1025 1687 
Q 1450 2450 1550 2900 
L 750 2900 
L 475 2850 
L 275 3050 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6570" d="M 1875 1525 
Q 1675 1200 1500 900 
Q 1825 825 2275 725 
Q 2500 975 2725 1525 
L 1875 1525 
z
M 850 4900 
Q 1325 4600 1425 4450 
Q 1525 4300 1525 4225 
Q 1525 4100 1437 3975 
Q 1350 3850 1300 3850 
Q 1225 3850 1150 4100 
Q 1050 4450 775 4850 
L 850 4900 
z
M 3000 4975 
L 3450 4675 
Q 3250 4625 3100 4425 
Q 2925 4225 2525 3825 
L 2450 3875 
Q 2925 4625 3000 4975 
z
M 1875 5250 
L 2425 5000 
L 2225 4825 
L 2225 3675 
L 2850 3675 
L 3125 3950 
L 3550 3525 
L 2225 3525 
L 2225 3300 
Q 2800 3175 2937 3062 
Q 3075 2950 3075 2775 
Q 3075 2700 3050 2587 
Q 3025 2475 2975 2475 
Q 2925 2475 2800 2650 
Q 2600 2925 2225 3175 
L 2225 2400 
L 1925 2200 
L 2275 2050 
Q 2150 2000 1950 1675 
L 2725 1675 
L 2900 1900 
L 3275 1575 
L 3050 1450 
Q 2800 875 2600 650 
Q 3000 550 3137 437 
Q 3275 325 3275 150 
Q 3275 -25 3200 -25 
Q 3125 -25 3025 75 
Q 2750 275 2400 425 
Q 1525 -275 325 -500 
L 300 -425 
Q 1450 -50 2100 550 
Q 1600 725 1125 800 
Q 1225 950 1525 1525 
L 1200 1525 
Q 875 1525 600 1450 
L 375 1675 
L 1575 1675 
Q 1675 1925 1775 2275 
L 1900 2225 
L 1900 3250 
Q 1225 2475 350 2050 
L 300 2125 
Q 1225 2775 1700 3525 
L 1250 3525 
Q 925 3525 650 3450 
L 425 3675 
L 1900 3675 
Q 1900 4575 1875 5250 
z
M 3925 3525 
Q 4050 2075 4525 1100 
Q 4925 1950 5025 3525 
L 3925 3525 
z
M 4050 5250 
L 4625 4925 
Q 4425 4850 4325 4625 
Q 4225 4400 3950 3675 
L 5375 3675 
L 5675 3975 
L 6125 3525 
L 5400 3525 
Q 5250 1675 4750 775 
Q 5325 75 6100 -100 
L 6100 -200 
Q 5625 -275 5600 -500 
Q 4950 -50 4525 525 
Q 3700 -275 2550 -650 
L 2500 -575 
Q 3650 -50 4325 800 
Q 3850 1975 3825 3375 
Q 3575 2725 3100 2150 
L 3025 2200 
Q 3625 3200 4050 5250 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-636e" d="M 3125 -650 
Q 3150 -100 3150 512 
Q 3150 1125 3125 1600 
L 3475 1400 
L 4175 1400 
L 4175 2400 
L 3000 2400 
Q 2825 625 1675 -525 
L 1600 -450 
Q 2150 275 2437 1187 
Q 2725 2100 2737 3312 
Q 2750 4525 2725 5050 
L 3100 4825 
L 5150 4825 
L 5350 5025 
L 5700 4700 
L 5500 4575 
Q 5500 3825 5525 3475 
L 5175 3325 
L 5175 3625 
L 3075 3625 
Q 3075 3150 3025 2550 
L 4175 2550 
Q 4175 3025 4150 3475 
L 4700 3225 
L 4500 3075 
L 4500 2550 
L 5325 2550 
L 5600 2825 
L 6025 2400 
L 4500 2400 
L 4500 1400 
L 5150 1400 
L 5350 1625 
L 5700 1300 
L 5500 1150 
Q 5500 150 5525 -400 
L 5175 -600 
L 5175 -75 
L 3475 -75 
L 3475 -500 
L 3125 -650 
z
M 3075 4675 
L 3075 3775 
L 5175 3775 
L 5175 4675 
L 3075 4675 
z
M 3475 1250 
L 3475 75 
L 5175 75 
L 5175 1250 
L 3475 1250 
z
M 1675 2450 
L 2500 2925 
L 2550 2825 
L 1675 2175 
L 1675 150 
Q 1650 -300 1125 -500 
Q 1175 -250 500 25 
L 500 125 
Q 1050 25 1187 37 
Q 1325 50 1325 225 
L 1325 2000 
Q 625 1500 600 1350 
L 225 1800 
Q 375 1825 1325 2300 
L 1325 3575 
L 750 3575 
L 450 3525 
L 250 3725 
L 1325 3725 
Q 1325 4750 1300 5175 
L 1875 4875 
L 1675 4700 
L 1675 3725 
L 1950 3725 
L 2275 4000 
L 2650 3575 
L 1675 3575 
L 1675 2450 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-9884" d="M 475 4825 
L 2225 4825 
L 2425 5075 
L 2825 4675 
Q 2600 4625 2387 4412 
Q 2175 4200 1800 3750 
Q 2050 3575 2037 3400 
Q 2025 3225 1937 3150 
Q 1850 3075 1825 3075 
Q 1725 3075 1650 3300 
Q 1525 3600 1050 4100 
L 1100 4150 
Q 1475 3975 1700 3825 
L 2225 4675 
L 1300 4675 
Q 975 4675 700 4600 
L 475 4825 
z
M 725 100 
Q 1175 50 1362 37 
Q 1550 25 1550 275 
L 1550 2875 
L 1125 2875 
Q 800 2875 525 2800 
L 300 3025 
L 2525 3025 
L 2725 3275 
L 3150 2875 
Q 2800 2825 2275 2075 
L 2200 2125 
L 2550 2875 
L 1875 2875 
L 1875 150 
Q 1875 -400 1350 -500 
Q 1325 -175 725 0 
L 725 100 
z
M 3250 775 
Q 3275 1425 3275 2287 
Q 3275 3150 3250 3900 
L 3600 3700 
L 4050 3700 
Q 4175 4400 4200 4675 
L 3650 4675 
Q 3325 4675 3050 4600 
L 2825 4825 
L 5325 4825 
L 5675 5150 
L 6125 4675 
L 4675 4675 
Q 4500 4425 4175 3700 
L 5300 3700 
L 5475 3975 
L 5875 3675 
L 5675 3475 
L 5675 1950 
Q 5675 1525 5700 1075 
L 5350 875 
L 5350 3550 
L 3600 3550 
L 3600 1000 
L 3250 775 
z
M 4850 2875 
L 4625 2675 
Q 4625 1475 4475 937 
Q 4325 400 3900 50 
Q 3475 -300 2425 -625 
L 2400 -550 
Q 3500 -50 3837 400 
Q 4175 850 4237 1587 
Q 4300 2325 4275 3175 
L 4850 2875 
z
M 4650 950 
Q 5725 375 5875 212 
Q 6025 50 6025 -125 
Q 6025 -225 5962 -387 
Q 5900 -550 5850 -550 
Q 5800 -550 5750 -450 
Q 5625 -250 5437 0 
Q 5250 250 4600 875 
L 4650 950 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6d4b" d="M 1975 1000 
Q 2000 1475 2000 2925 
Q 2000 4375 1975 5000 
L 2350 4775 
L 3575 4775 
L 3775 4975 
L 4100 4650 
L 3900 4500 
L 3900 2350 
Q 3900 1850 3925 1350 
L 3600 1200 
L 3600 4625 
L 2325 4625 
L 2325 1150 
L 1975 1000 
z
M 2775 3975 
L 3300 3700 
L 3125 3525 
Q 3150 1825 2875 937 
Q 2600 50 1375 -600 
L 1325 -500 
Q 2200 100 2475 725 
Q 2750 1350 2775 2187 
Q 2800 3025 2775 3975 
z
M 3100 950 
Q 3825 450 3937 262 
Q 4050 75 4050 -25 
Q 4050 -150 3975 -262 
Q 3900 -375 3850 -375 
Q 3750 -375 3650 -75 
Q 3500 375 3050 875 
L 3100 950 
z
M 4475 825 
Q 4500 1200 4500 1700 
L 4500 3400 
Q 4500 3950 4475 4300 
L 5025 4025 
L 4825 3825 
L 4825 1700 
Q 4825 1325 4850 950 
L 4475 825 
z
M 5375 150 
L 5375 4050 
Q 5375 4750 5350 5125 
L 5875 4875 
L 5700 4700 
L 5700 75 
Q 5700 -200 5600 -350 
Q 5500 -500 5200 -600 
Q 5025 -225 4450 -50 
L 4450 50 
Q 5125 -50 5250 -37 
Q 5375 -25 5375 150 
z
M 1850 3500 
Q 1375 1525 1300 1225 
Q 1200 900 1212 450 
Q 1225 0 1250 -175 
Q 1250 -275 1125 -275 
Q 1025 -275 875 -225 
Q 725 -175 725 0 
Q 725 125 800 375 
Q 875 625 875 775 
Q 875 950 762 1050 
Q 650 1150 275 1250 
L 275 1350 
Q 725 1300 837 1312 
Q 950 1325 1087 1537 
Q 1225 1750 1750 3525 
L 1850 3500 
z
M 375 3775 
Q 1075 3350 1112 3100 
Q 1150 2850 1050 2775 
Q 950 2700 900 2700 
Q 800 2700 725 2975 
Q 600 3350 325 3700 
L 375 3775 
z
M 800 5100 
Q 1475 4725 1550 4512 
Q 1625 4300 1525 4187 
Q 1425 4075 1350 4075 
Q 1275 4075 1200 4300 
Q 1075 4650 750 5025 
L 800 5100 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-7ed3" d="M 4050 3900 
Q 4050 4925 4025 5250 
L 4625 4925 
L 4375 4750 
L 4375 3900 
L 5300 3900 
L 5600 4200 
L 6025 3750 
L 4375 3750 
L 4375 2625 
L 5050 2625 
L 5350 2925 
L 5775 2475 
L 3650 2475 
Q 3325 2475 3050 2400 
L 2825 2625 
L 4050 2625 
L 4050 3750 
L 3425 3750 
Q 3100 3750 2825 3675 
L 2600 3900 
L 4050 3900 
z
M 3025 -600 
Q 3050 -50 3050 675 
Q 3050 1400 3025 1925 
L 3425 1700 
L 5125 1700 
L 5350 1925 
L 5700 1575 
L 5475 1425 
L 5475 400 
Q 5475 -150 5500 -375 
L 5150 -525 
L 5150 -25 
L 3375 -25 
L 3375 -425 
L 3025 -600 
z
M 3375 1550 
L 3375 125 
L 5150 125 
L 5150 1550 
L 3375 1550 
z
M 975 1550 
L 2700 1825 
L 2700 1725 
Q 2400 1650 1750 1462 
Q 1100 1275 800 1050 
L 525 1500 
Q 725 1575 900 1750 
Q 1075 1925 1725 2825 
Q 1425 2775 1150 2712 
Q 875 2650 625 2500 
L 400 2950 
Q 650 2950 987 3662 
Q 1325 4375 1525 5125 
L 2050 4850 
L 1825 4725 
Q 1300 3725 800 2950 
L 1800 2975 
Q 2100 3450 2275 3850 
L 2725 3525 
L 2500 3400 
Q 1800 2475 975 1550 
z
M 650 -250 
L 400 225 
Q 750 275 1237 387 
Q 1725 500 2750 800 
L 2775 700 
Q 925 -25 650 -250 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-679c" d="M 1725 4725 
L 1725 3875 
L 3025 3875 
L 3025 4725 
L 1725 4725 
z
M 3400 4725 
L 3400 3875 
L 4725 3875 
L 4725 4725 
L 3400 4725 
z
M 1725 3725 
L 1725 2825 
L 3025 2825 
L 3025 3725 
L 1725 3725 
z
M 3400 3725 
L 3400 2825 
L 4725 2825 
L 4725 3725 
L 3400 3725 
z
M 1350 2300 
Q 1375 3000 1375 3700 
Q 1375 4400 1350 5075 
L 1725 4875 
L 4700 4875 
L 4925 5100 
L 5300 4750 
L 5075 4600 
Q 5075 2850 5100 2500 
L 4725 2325 
L 4725 2675 
L 3400 2675 
L 3400 1950 
L 5100 1950 
L 5475 2325 
L 6000 1800 
L 3450 1800 
Q 4500 450 6025 100 
L 6025 0 
Q 5550 -25 5450 -300 
Q 3950 525 3400 1700 
Q 3400 -50 3425 -425 
L 3000 -625 
Q 3025 350 3025 1650 
Q 2100 325 400 -375 
L 375 -275 
Q 1850 550 2700 1800 
L 1300 1800 
Q 975 1800 700 1725 
L 475 1950 
L 3025 1950 
L 3025 2675 
L 1725 2675 
L 1725 2450 
L 1350 2300 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimSun-632f"/>
     <use xlink:href="#SimSun-52a8" x="100"/>
     <use xlink:href="#SimSun-6570" x="200"/>
     <use xlink:href="#SimSun-636e" x="300"/>
     <use xlink:href="#SimSun-9884" x="400"/>
     <use xlink:href="#SimSun-6d4b" x="500"/>
     <use xlink:href="#SimSun-7ed3" x="600"/>
     <use xlink:href="#SimSun-679c" x="700"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="line2d_29">
     <path d="M 904.100391 37.333594 
L 914.600391 37.333594 
L 925.100391 37.333594 
" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_17">
     <!-- 真实振动位移 -->
     <g transform="translate(933.500391 41.008594)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-771f" d="M 2050 3450 
L 2050 2900 
L 4300 2900 
L 4300 3450 
L 2050 3450 
z
M 2050 2750 
L 2050 2225 
L 4300 2225 
L 4300 2750 
L 2050 2750 
z
M 2050 2075 
L 2050 1550 
L 4300 1550 
L 4300 2075 
L 2050 2075 
z
M 2050 1400 
L 2050 875 
L 4300 875 
L 4300 1400 
L 2050 1400 
z
M 2875 3600 
L 2925 4325 
L 1525 4325 
Q 1200 4325 925 4250 
L 700 4475 
L 2950 4475 
Q 2975 4825 2975 5275 
L 3550 5025 
L 3375 4925 
L 3300 4475 
L 5000 4475 
L 5325 4825 
L 5775 4325 
L 3275 4325 
L 3200 3600 
L 4275 3600 
L 4450 3875 
L 4850 3575 
L 4650 3375 
L 4650 875 
L 5300 875 
L 5675 1225 
L 6125 725 
L 1150 725 
Q 825 725 550 650 
L 325 875 
L 1700 875 
Q 1700 3200 1675 3775 
L 2050 3600 
L 2875 3600 
z
M 2425 725 
L 2825 350 
L 2575 300 
Q 1500 -425 800 -650 
L 775 -575 
Q 1950 125 2425 725 
z
M 3800 700 
Q 4575 375 4962 200 
Q 5350 25 5400 -112 
Q 5450 -250 5450 -325 
Q 5450 -400 5400 -525 
Q 5350 -650 5325 -650 
Q 5250 -650 5100 -450 
Q 4875 -150 3750 625 
L 3800 700 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5b9e" d="M 1950 3625 
Q 2575 3400 2700 3250 
Q 2825 3100 2825 2975 
Q 2825 2850 2750 2700 
Q 2675 2550 2625 2550 
Q 2550 2550 2450 2800 
Q 2275 3175 1900 3550 
L 1950 3625 
z
M 3575 1325 
Q 3500 1100 3425 925 
Q 4650 550 5087 325 
Q 5525 100 5512 -225 
Q 5500 -550 5400 -550 
Q 5300 -550 5075 -300 
Q 4625 175 3375 800 
Q 3025 175 2300 -150 
Q 1575 -475 425 -625 
L 425 -525 
Q 1475 -325 2200 100 
Q 2925 525 3200 1325 
L 1225 1325 
Q 900 1325 625 1250 
L 400 1475 
L 3225 1475 
Q 3325 1950 3375 2575 
Q 3425 3200 3425 3700 
L 4050 3400 
L 3850 3225 
Q 3750 2300 3625 1475 
L 5175 1475 
L 5550 1850 
L 6075 1325 
L 3575 1325 
z
M 1275 2700 
Q 1975 2425 2100 2262 
Q 2225 2100 2225 1975 
Q 2225 1850 2150 1700 
Q 2075 1550 2025 1550 
Q 1950 1550 1850 1800 
Q 1675 2175 1225 2625 
L 1275 2700 
z
M 2750 5225 
L 2825 5275 
Q 3225 5100 3425 4925 
Q 3625 4750 3625 4600 
Q 3625 4450 3512 4300 
Q 3400 4150 3325 4150 
Q 3250 4150 3200 4400 
Q 3100 4775 2750 5225 
z
M 4975 3225 
L 5350 3925 
L 1175 3925 
Q 1225 3525 1125 3300 
Q 1025 3075 787 3100 
Q 550 3125 550 3200 
Q 550 3300 700 3475 
Q 975 3825 1050 4375 
L 1150 4375 
L 1175 4075 
L 5300 4075 
L 5575 4350 
L 6050 3800 
Q 5625 3825 5050 3175 
L 4975 3225 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-771f"/>
      <use xlink:href="#SimSun-5b9e" x="100"/>
      <use xlink:href="#SimSun-632f" x="200"/>
      <use xlink:href="#SimSun-52a8" x="300"/>
      <use xlink:href="#SimSun-4f4d" x="400"/>
      <use xlink:href="#SimSun-79fb" x="500"/>
     </g>
    </g>
    <g id="line2d_30">
     <path d="M 904.100391 52.222266 
L 914.600391 52.222266 
L 925.100391 52.222266 
" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
    </g>
    <g id="text_18">
     <!-- LSTM+卡尔曼滤波预测位移 -->
     <g transform="translate(933.500391 55.897266)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-4c" d="M 1000 450 
Q 1000 350 1062 300 
Q 1125 250 1225 250 
L 1900 250 
Q 2250 250 2500 462 
Q 2750 675 2875 1125 
L 3000 1075 
L 2775 100 
L 200 100 
L 200 250 
L 375 250 
Q 500 250 550 300 
Q 600 350 600 450 
L 600 4025 
Q 600 4125 550 4175 
Q 500 4225 375 4225 
L 225 4225 
L 225 4375 
L 1400 4375 
L 1400 4225 
L 1225 4225 
Q 1125 4225 1062 4175 
Q 1000 4125 1000 4025 
L 1000 450 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-53" d="M 1500 2150 
Q 825 2450 575 2675 
Q 325 2900 325 3375 
Q 325 3750 637 4087 
Q 950 4425 1525 4425 
Q 1775 4425 2000 4350 
Q 2225 4250 2325 4250 
Q 2400 4250 2462 4287 
Q 2525 4325 2575 4425 
L 2675 3400 
L 2550 3350 
Q 2325 3875 2087 4062 
Q 1850 4250 1475 4250 
Q 1000 4250 825 3987 
Q 650 3725 650 3425 
Q 650 3150 812 2987 
Q 975 2825 1700 2500 
Q 2300 2250 2562 1937 
Q 2825 1625 2825 1200 
Q 2825 750 2512 400 
Q 2200 50 1600 50 
Q 1350 50 1150 150 
Q 925 225 800 225 
Q 725 225 650 175 
Q 575 125 475 50 
L 275 1275 
L 425 1325 
Q 600 700 912 462 
Q 1225 225 1625 225 
Q 2050 225 2262 475 
Q 2475 725 2475 1200 
Q 2475 1450 2237 1687 
Q 2000 1925 1500 2150 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-54" d="M 2225 100 
L 925 100 
L 925 250 
L 1150 250 
Q 1275 250 1325 300 
Q 1375 350 1375 450 
L 1375 3950 
Q 1375 4150 1350 4175 
Q 1325 4200 1150 4200 
Q 800 4200 662 4075 
Q 525 3950 325 3400 
L 175 3425 
L 400 4375 
L 2775 4375 
L 2975 3425 
L 2850 3400 
Q 2650 3925 2537 4062 
Q 2425 4200 2075 4200 
Q 1825 4200 1800 4175 
Q 1775 4150 1775 3925 
L 1775 450 
Q 1775 350 1837 300 
Q 1900 250 2000 250 
L 2225 250 
L 2225 100 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-4d" d="M 2800 450 
Q 2800 350 2862 300 
Q 2925 250 3025 250 
L 3125 250 
L 3125 100 
L 2050 100 
L 2050 250 
L 2175 250 
Q 2300 250 2350 300 
Q 2400 350 2400 450 
L 2400 3725 
L 2350 3725 
L 1500 100 
L 1350 100 
L 575 3650 
L 525 3650 
L 525 450 
Q 525 350 587 300 
Q 650 250 750 250 
L 825 250 
L 825 100 
L 75 100 
L 75 250 
L 150 250 
Q 275 250 325 300 
Q 375 350 375 450 
L 375 4025 
Q 375 4125 325 4175 
Q 275 4225 150 4225 
L 50 4225 
L 50 4375 
L 775 4375 
L 1500 1025 
L 1550 1025 
L 2325 4375 
L 3125 4375 
L 3125 4225 
L 3025 4225 
Q 2925 4225 2862 4175 
Q 2800 4125 2800 4025 
L 2800 450 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-2b" d="M 1525 2450 
L 1525 3875 
L 1750 3875 
L 1750 2450 
L 3000 2450 
L 3000 2225 
L 1750 2225 
L 1750 800 
L 1525 800 
L 1525 2225 
L 275 2225 
L 275 2450 
L 1525 2450 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5361" d="M 2675 -600 
Q 2700 75 2700 475 
L 2700 2675 
L 1150 2675 
Q 825 2675 550 2600 
L 325 2825 
L 2700 2825 
L 2700 4400 
Q 2700 4800 2675 5250 
L 3275 4950 
L 3050 4775 
L 3050 4100 
L 4650 4100 
L 4975 4425 
L 5450 3950 
L 3050 3950 
L 3050 2825 
L 5175 2825 
L 5550 3200 
L 6075 2675 
L 2800 2675 
L 3275 2400 
L 3050 2250 
L 3050 1975 
Q 4875 1550 5100 1300 
Q 5325 1050 5287 862 
Q 5250 675 5175 675 
Q 5075 675 4850 875 
Q 4475 1225 3050 1850 
L 3050 350 
Q 3050 -100 3075 -425 
L 2675 -600 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5c14" d="M 4650 3050 
L 5100 3900 
L 1925 3900 
Q 1375 3100 825 2575 
L 750 2650 
Q 1225 3250 1562 3925 
Q 1900 4600 2025 5225 
L 2550 4975 
Q 2425 4850 2300 4650 
Q 2175 4450 1975 4050 
L 5100 4050 
L 5375 4350 
L 5850 3875 
Q 5450 3850 4725 3000 
L 4650 3050 
z
M 3150 325 
L 3150 2300 
Q 3150 3025 3125 3400 
L 3700 3100 
L 3500 2950 
L 3500 125 
Q 3450 -425 2925 -625 
Q 2900 -275 2125 -25 
L 2125 100 
Q 2825 0 2975 12 
Q 3125 25 3150 325 
z
M 2000 2550 
L 2550 2175 
Q 2350 2075 2225 1875 
Q 2100 1675 1625 1062 
Q 1150 450 425 -100 
L 375 -25 
Q 975 575 1400 1250 
Q 1825 1925 2000 2550 
z
M 4100 2425 
Q 4800 1875 5212 1437 
Q 5625 1000 5700 737 
Q 5775 475 5675 337 
Q 5575 200 5500 200 
Q 5400 200 5300 475 
Q 5125 900 4812 1375 
Q 4500 1850 4025 2350 
L 4100 2425 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-66fc" d="M 2125 1275 
Q 2600 800 3175 475 
Q 3725 800 4175 1275 
L 2125 1275 
z
M 4850 4675 
Q 4850 3700 4875 3325 
L 4525 3175 
L 4525 3400 
L 1900 3400 
L 1900 3275 
L 1550 3150 
Q 1575 3600 1575 4075 
Q 1575 4550 1550 5125 
L 1900 4925 
L 4475 4925 
L 4700 5150 
L 5075 4850 
L 4850 4675 
z
M 1900 4775 
L 1900 4250 
L 4525 4250 
L 4525 4775 
L 1900 4775 
z
M 1900 4100 
L 1900 3550 
L 4525 3550 
L 4525 4100 
L 1900 4100 
z
M 900 1575 
Q 925 2000 925 2400 
Q 925 2800 900 3150 
L 1275 2975 
L 5050 2975 
L 5275 3200 
L 5650 2900 
L 5425 2750 
Q 5425 2050 5450 1775 
L 5100 1625 
L 5100 1875 
L 1250 1875 
L 1250 1725 
L 900 1575 
z
M 1250 2825 
L 1250 2025 
L 2275 2025 
L 2275 2825 
L 1250 2825 
z
M 2625 2825 
L 2625 2025 
L 3700 2025 
L 3700 2825 
L 2625 2825 
z
M 4025 2825 
L 4025 2025 
L 5100 2025 
L 5100 2825 
L 4025 2825 
z
M 4525 1100 
Q 3875 575 3450 325 
Q 3900 100 4487 12 
Q 5075 -75 5425 -75 
Q 5750 -75 6075 -25 
L 6075 -125 
Q 5600 -250 5575 -500 
Q 4850 -475 4237 -312 
Q 3625 -150 3175 150 
Q 2475 -200 1737 -362 
Q 1000 -525 400 -600 
L 350 -500 
Q 1000 -400 1687 -187 
Q 2375 25 2925 325 
Q 2425 725 2000 1275 
L 1775 1275 
L 1475 1225 
L 1275 1425 
L 4200 1425 
L 4450 1625 
L 4825 1200 
L 4525 1100 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6ee4" d="M 3025 1175 
L 3125 1150 
Q 3125 575 3050 337 
Q 2975 100 2825 50 
Q 2675 0 2562 37 
Q 2450 75 2450 125 
Q 2450 225 2600 350 
Q 2825 550 3025 1175 
z
M 3950 1775 
Q 4475 1550 4587 1412 
Q 4700 1275 4700 1175 
Q 4700 1050 4637 925 
Q 4575 800 4525 800 
Q 4450 800 4375 1025 
Q 4250 1350 3900 1700 
L 3950 1775 
z
M 5275 1175 
Q 5725 925 5875 762 
Q 6025 600 6025 450 
Q 6025 325 5950 200 
Q 5875 75 5850 75 
Q 5775 75 5725 300 
Q 5625 650 5225 1125 
L 5275 1175 
z
M 3925 -350 
Q 3475 -375 3450 75 
L 3450 850 
Q 3450 1175 3425 1475 
L 3975 1200 
L 3775 1050 
L 3775 200 
Q 3750 -75 4100 -75 
L 4825 -75 
Q 5025 -50 5050 212 
Q 5075 475 5100 800 
L 5200 800 
Q 5200 475 5237 250 
Q 5275 25 5500 -50 
Q 5325 -350 4875 -350 
L 3925 -350 
z
M 3725 3750 
Q 3725 4875 3700 5250 
L 4275 5025 
L 4050 4825 
L 4050 4450 
L 4950 4450 
L 5275 4775 
L 5700 4300 
L 4050 4300 
L 4050 3750 
L 5450 3750 
L 5650 4000 
L 6050 3575 
Q 5750 3600 5325 3100 
L 5250 3150 
L 5500 3600 
L 2700 3600 
Q 2725 2000 2525 1112 
Q 2325 225 1475 -575 
L 1400 -525 
Q 1875 25 2087 662 
Q 2300 1300 2337 1962 
Q 2375 2625 2375 3075 
Q 2375 3525 2350 3975 
L 2700 3750 
L 3725 3750 
z
M 5125 2200 
Q 5300 2250 5325 2525 
Q 5350 2775 5375 2975 
L 5475 2975 
Q 5475 2700 5512 2487 
Q 5550 2275 5750 2200 
Q 5575 1925 5150 1950 
L 4225 1950 
Q 3650 1925 3700 2325 
L 3700 2725 
L 3075 2675 
L 2950 2600 
L 2750 2800 
L 3700 2875 
Q 3700 3275 3675 3600 
L 4225 3375 
L 4025 3225 
L 4025 2925 
L 4575 3000 
L 4825 3275 
L 5225 2900 
L 4025 2775 
L 4025 2375 
Q 4025 2175 4350 2200 
L 5125 2200 
z
M 2200 4025 
Q 1400 1400 1337 1062 
Q 1275 725 1275 275 
Q 1275 -175 1275 -325 
Q 1275 -475 1175 -475 
Q 1075 -475 925 -412 
Q 775 -350 775 -175 
Q 775 -50 850 200 
Q 925 450 925 600 
Q 925 775 812 875 
Q 700 975 325 1075 
L 325 1150 
Q 775 1125 875 1150 
Q 975 1175 1112 1375 
Q 1250 1575 2100 4050 
L 2200 4025 
z
M 350 3475 
Q 1125 3100 1162 2887 
Q 1200 2675 1100 2550 
Q 1000 2425 950 2425 
Q 850 2425 800 2625 
Q 675 2950 300 3400 
L 350 3475 
z
M 925 4900 
Q 1650 4575 1725 4362 
Q 1800 4150 1687 3987 
Q 1575 3825 1525 3825 
Q 1450 3825 1375 4050 
Q 1250 4375 875 4850 
L 925 4900 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6ce2" d="M 4975 3175 
L 5300 3800 
L 4200 3800 
L 4200 2525 
L 5000 2525 
L 5225 2750 
L 5575 2400 
L 5325 2225 
Q 4975 1450 4400 750 
Q 5200 25 6075 -150 
L 6075 -225 
Q 5700 -250 5525 -525 
Q 4750 -75 4200 525 
Q 3325 -325 1950 -625 
L 1925 -525 
Q 3175 -125 4000 750 
Q 3500 1400 3225 2375 
L 3075 2375 
L 2925 2325 
L 2775 2450 
Q 2775 1550 2450 787 
Q 2125 25 1350 -600 
L 1275 -525 
Q 1950 200 2200 925 
Q 2450 1650 2450 2550 
Q 2450 3450 2425 4175 
L 2800 3950 
L 3875 3950 
Q 3875 4950 3850 5250 
L 4375 5050 
L 4200 4850 
L 4200 3950 
L 5250 3950 
L 5525 4225 
L 5950 3800 
Q 5725 3725 5562 3625 
Q 5400 3525 5050 3125 
L 4975 3175 
z
M 2775 3800 
L 2775 2525 
L 3875 2525 
L 3875 3800 
L 2775 3800 
z
M 4200 975 
Q 4675 1550 5025 2375 
L 3350 2375 
Q 3725 1450 4200 975 
z
M 2350 4100 
Q 1375 1450 1287 1137 
Q 1200 825 1212 375 
Q 1225 -75 1250 -250 
Q 1250 -350 1125 -350 
Q 1025 -350 875 -300 
Q 725 -250 725 -75 
Q 725 50 800 300 
Q 875 550 875 700 
Q 875 875 762 975 
Q 650 1075 275 1175 
L 275 1275 
Q 725 1225 837 1237 
Q 950 1250 1087 1462 
Q 1225 1675 2250 4125 
L 2350 4100 
z
M 325 3650 
Q 1200 3200 1200 2912 
Q 1200 2625 1062 2525 
Q 925 2425 825 2775 
Q 700 3100 275 3575 
L 325 3650 
z
M 875 5075 
Q 1575 4725 1637 4550 
Q 1700 4375 1700 4300 
Q 1700 4175 1612 4075 
Q 1525 3975 1475 3975 
Q 1400 3975 1325 4225 
Q 1200 4550 825 5000 
L 875 5075 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-5361" x="250"/>
      <use xlink:href="#SimSun-5c14" x="350"/>
      <use xlink:href="#SimSun-66fc" x="450"/>
      <use xlink:href="#SimSun-6ee4" x="550"/>
      <use xlink:href="#SimSun-6ce2" x="650"/>
      <use xlink:href="#SimSun-9884" x="750"/>
      <use xlink:href="#SimSun-6d4b" x="850"/>
      <use xlink:href="#SimSun-4f4d" x="950"/>
      <use xlink:href="#SimSun-79fb" x="1050"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 48.756641 673.440156 
L 1063.700391 673.440156 
L 1063.700391 377.487656 
L 48.756641 377.487656 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_7">
     <g id="line2d_31">
      <path d="M 69.038788 673.440156 
L 69.038788 377.487656 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m85c669cb6f" x="69.038788" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 1005 -->
      <g transform="translate(58.538788 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
       <use xlink:href="#SimSun-35" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_33">
      <path d="M 251.521089 673.440156 
L 251.521089 377.487656 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m85c669cb6f" x="251.521089" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 1010 -->
      <g transform="translate(241.021089 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-31" x="100"/>
       <use xlink:href="#SimSun-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_35">
      <path d="M 434.003391 673.440156 
L 434.003391 377.487656 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m85c669cb6f" x="434.003391" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 1015 -->
      <g transform="translate(423.503391 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-31" x="100"/>
       <use xlink:href="#SimSun-35" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 616.485692 673.440156 
L 616.485692 377.487656 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m85c669cb6f" x="616.485692" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 1020 -->
      <g transform="translate(605.985692 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-32" x="100"/>
       <use xlink:href="#SimSun-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 798.967994 673.440156 
L 798.967994 377.487656 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m85c669cb6f" x="798.967994" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 1025 -->
      <g transform="translate(788.467994 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-32" x="100"/>
       <use xlink:href="#SimSun-35" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 981.450295 673.440156 
L 981.450295 377.487656 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m85c669cb6f" x="981.450295" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 1030 -->
      <g transform="translate(970.950295 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-33" x="100"/>
       <use xlink:href="#SimSun-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="text_25">
     <!-- 时间 (ms) -->
     <g transform="translate(532.603516 701.461641)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-73" d="M 625 2175 
Q 625 2550 912 2750 
Q 1200 2950 1600 2950 
Q 1825 2950 2025 2900 
Q 2225 2825 2300 2825 
Q 2375 2825 2425 2850 
Q 2475 2875 2550 2950 
L 2650 2125 
L 2500 2100 
Q 2400 2425 2162 2612 
Q 1925 2800 1625 2800 
Q 1300 2800 1112 2675 
Q 925 2550 925 2325 
Q 925 2125 1012 2012 
Q 1100 1900 1325 1850 
Q 1500 1775 1775 1700 
Q 2025 1600 2250 1500 
Q 2450 1400 2587 1237 
Q 2725 1075 2725 875 
Q 2725 500 2462 287 
Q 2200 75 1700 75 
Q 1375 75 1225 150 
Q 1050 200 950 200 
Q 875 200 787 162 
Q 700 125 600 75 
L 525 1000 
L 675 1025 
Q 750 625 1000 425 
Q 1250 225 1700 225 
Q 2050 225 2237 350 
Q 2425 475 2425 725 
Q 2425 900 2325 1012 
Q 2225 1125 2125 1175 
Q 1875 1275 1575 1400 
Q 1275 1500 1050 1600 
Q 825 1700 725 1850 
Q 625 2000 625 2175 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-65f6"/>
      <use xlink:href="#SimSun-95f4" x="100"/>
      <use xlink:href="#SimSun-20" x="200"/>
      <use xlink:href="#SimSun-28" x="250"/>
      <use xlink:href="#SimSun-6d" x="300"/>
      <use xlink:href="#SimSun-73" x="350"/>
      <use xlink:href="#SimSun-29" x="400"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_8">
     <g id="line2d_43">
      <path d="M 48.756641 650.677745 
L 1063.700391 650.677745 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="650.677745" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- -0.2 -->
      <g transform="translate(20.756641 654.430674)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_45">
      <path d="M 48.756641 606.906833 
L 1063.700391 606.906833 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="606.906833" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_27">
      <!-- -0.1 -->
      <g transform="translate(20.756641 610.659763)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-31" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_47">
      <path d="M 48.756641 563.135922 
L 1063.700391 563.135922 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="563.135922" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 0.0 -->
      <g transform="translate(26.006641 566.888852)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_49">
      <path d="M 48.756641 519.365011 
L 1063.700391 519.365011 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="519.365011" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 0.1 -->
      <g transform="translate(26.006641 523.117941)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-31" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_51">
      <path d="M 48.756641 475.5941 
L 1063.700391 475.5941 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="475.5941" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 0.2 -->
      <g transform="translate(26.006641 479.34703)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_53">
      <path d="M 48.756641 431.823189 
L 1063.700391 431.823189 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="431.823189" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 0.3 -->
      <g transform="translate(26.006641 435.576119)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-33" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_55">
      <path d="M 48.756641 388.052278 
L 1063.700391 388.052278 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mcdacb2ce2e" x="48.756641" y="388.052278" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 0.4 -->
      <g transform="translate(26.006641 391.805207)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_33">
     <!-- 位移 (μm) -->
     <g transform="translate(15.403125 551.713906)rotate(-90)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-4f4d"/>
      <use xlink:href="#SimSun-79fb" x="100"/>
      <use xlink:href="#SimSun-20" x="200"/>
      <use xlink:href="#SimSun-28" x="250"/>
      <use xlink:href="#SimSun-3bc" x="300"/>
      <use xlink:href="#SimSun-6d" x="400"/>
      <use xlink:href="#SimSun-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="line2d_57">
    <path d="M 94.890447 659.98777 
L 113.899021 644.477663 
L 132.527422 627.152428 
L 151.535995 609.292897 
L 170.164397 593.570728 
L 189.17297 580.564287 
L 207.801371 570.838046 
L 226.809944 563.057615 
L 245.438346 556.203624 
L 264.446919 548.734971 
L 283.455492 539.880929 
L 302.083894 529.346639 
L 321.092467 516.535371 
L 339.720868 502.247705 
L 358.729442 486.806192 
L 377.357843 472.236302 
L 396.366416 459.480319 
L 414.994818 449.969814 
L 434.003391 443.172284 
L 452.631792 438.226955 
L 471.640366 433.423133 
L 490.268767 427.990099 
L 509.27734 421.720133 
L 527.905742 415.64021 
L 546.914315 410.351812 
L 565.542716 406.294251 
L 584.551289 402.822427 
L 603.179691 399.423543 
L 622.188264 395.795324 
L 640.816666 392.680389 
L 659.825239 390.940043 
L 678.45364 391.224034 
L 697.462213 393.241596 
L 716.090615 395.936852 
L 735.099188 398.497993 
L 753.72759 400.721204 
L 772.736163 403.486427 
L 791.364564 407.643404 
L 810.373137 413.610492 
L 829.001539 420.303181 
L 848.010112 426.594505 
L 866.638514 431.526139 
L 885.647087 436.174326 
L 904.275488 442.601007 
L 923.284061 453.473651 
L 941.912463 469.12365 
L 960.921036 488.409772 
L 979.549438 506.852096 
L 998.558011 521.155347 
L 1017.566584 528.57936 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_58">
    <path d="M 94.890447 659.98777 
L 113.899021 640.590506 
L 132.527422 621.595758 
L 151.535995 606.081999 
L 170.164397 584.849568 
L 189.17297 559.096457 
L 207.801371 542.854251 
L 226.809944 556.785117 
L 245.438346 552.772442 
L 264.446919 526.096028 
L 283.455492 526.229558 
L 302.083894 541.678781 
L 321.092467 516.639961 
L 339.720868 528.187569 
L 358.729442 487.419054 
L 377.357843 511.567922 
L 396.366416 453.309243 
L 414.994818 431.230787 
L 434.003391 469.838734 
L 452.631792 444.123755 
L 471.640366 438.125528 
L 490.268767 440.18735 
L 509.27734 436.466491 
L 527.905742 476.463942 
L 546.914315 424.836766 
L 565.542716 457.239032 
L 584.551289 427.31401 
L 603.179691 411.408576 
L 622.188264 406.619399 
L 640.816666 403.77225 
L 659.825239 406.249252 
L 678.45364 406.393073 
L 697.462213 408.516848 
L 716.090615 405.489709 
L 735.099188 468.8579 
L 753.72759 419.263137 
L 772.736163 451.95621 
L 791.364564 440.959504 
L 810.373137 412.805612 
L 829.001539 434.217828 
L 848.010112 429.398261 
L 866.638514 445.535052 
L 885.647087 461.18971 
L 904.275488 454.007907 
L 923.284061 475.591642 
L 941.912463 478.60979 
L 960.921036 493.21213 
L 979.549438 483.143704 
L 998.558011 530.980918 
L 1017.566584 509.7171 
" clip-path="url(#pfb01c432e2)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
   </g>
   <g id="patch_8">
    <path d="M 48.756641 673.440156 
L 48.756641 377.487656 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 1063.700391 673.440156 
L 1063.700391 377.487656 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 48.756641 673.440156 
L 1063.700391 673.440156 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 48.756641 377.487656 
L 1063.700391 377.487656 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_34">
    <!-- 振动位移时间序列对比 -->
    <g transform="translate(493.228516 371.487656)scale(0.126 -0.126)">
     <defs>
      <path id="SimSun-5e8f" d="M 1000 4575 
L 1375 4350 
L 5050 4350 
L 5425 4725 
L 5950 4200 
L 1375 4200 
Q 1375 3225 1350 2462 
Q 1325 1700 1112 962 
Q 900 225 300 -575 
L 225 -525 
Q 725 350 862 1087 
Q 1000 1825 1012 2687 
Q 1025 3550 1000 4575 
z
M 3000 5225 
Q 3450 5075 3625 4937 
Q 3800 4800 3762 4637 
Q 3725 4475 3612 4400 
Q 3500 4325 3400 4600 
Q 3275 4875 2950 5150 
L 3000 5225 
z
M 2675 0 
Q 3100 -50 3325 -50 
Q 3550 -50 3575 225 
L 3575 2050 
L 2325 2050 
Q 2000 2050 1725 1975 
L 1500 2200 
L 3625 2200 
Q 3625 2400 3400 2637 
Q 3175 2875 2925 3075 
L 2975 3150 
Q 3325 3050 3775 2800 
L 4750 3550 
L 2725 3550 
Q 2400 3550 2125 3475 
L 1900 3700 
L 4775 3700 
L 5050 3950 
L 5475 3425 
Q 5200 3425 4837 3262 
Q 4475 3100 3875 2725 
Q 4000 2575 3975 2450 
Q 3950 2325 3825 2200 
L 5325 2200 
L 5550 2475 
L 6025 2000 
Q 5600 2000 5100 1350 
L 5000 1400 
L 5350 2050 
L 3900 2050 
L 3900 125 
Q 3900 -375 3400 -600 
Q 3325 -275 2675 -125 
L 2675 0 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5217" d="M 550 4550 
L 3175 4550 
L 3500 4875 
L 3975 4400 
L 2325 4400 
Q 2125 3800 1925 3325 
L 2925 3325 
L 3150 3575 
L 3575 3200 
L 3325 3050 
Q 3075 2075 2787 1525 
Q 2500 975 1975 462 
Q 1450 -50 425 -525 
L 375 -450 
Q 1475 200 2062 1000 
Q 2650 1800 2975 3175 
L 1875 3175 
Q 1750 2925 1600 2650 
Q 2250 2125 2212 1850 
Q 2175 1575 2075 1500 
L 1950 1425 
Q 1850 1425 1825 1725 
Q 1775 2100 1550 2550 
Q 1100 1925 550 1375 
L 475 1450 
Q 1050 2225 1375 2925 
Q 1700 3625 1875 4400 
L 1375 4400 
Q 1050 4400 775 4325 
L 550 4550 
z
M 4100 650 
Q 4125 1525 4125 2475 
Q 4125 3425 4100 4100 
L 4650 3875 
L 4450 3675 
L 4450 1625 
Q 4450 1200 4475 800 
L 4100 650 
z
M 5325 3950 
Q 5325 4850 5300 5150 
L 5875 4900 
L 5675 4700 
L 5675 225 
Q 5700 -400 5050 -600 
Q 5025 -175 4125 0 
L 4125 100 
Q 4825 50 5075 37 
Q 5325 25 5325 350 
L 5325 3950 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5bf9" d="M 400 4250 
L 2375 4250 
L 2575 4500 
L 3000 4150 
L 2750 3950 
Q 2400 2850 2075 2100 
Q 2575 1375 2675 1062 
Q 2775 750 2762 537 
Q 2750 325 2662 237 
Q 2575 150 2550 150 
Q 2450 150 2375 575 
Q 2250 1075 1925 1800 
Q 1675 1350 1275 875 
Q 875 400 250 -100 
L 225 25 
Q 675 450 1087 1037 
Q 1500 1625 1725 2100 
Q 1000 3125 675 3550 
L 750 3625 
Q 1300 3025 1850 2400 
Q 2100 3050 2375 4100 
L 1225 4100 
Q 900 4100 625 4025 
L 400 4250 
z
M 3600 125 
Q 4375 0 4537 25 
Q 4700 50 4700 225 
L 4700 3350 
L 3525 3350 
Q 3200 3350 2925 3275 
L 2700 3500 
L 4700 3500 
L 4700 4350 
Q 4700 4800 4675 5125 
L 5275 4825 
L 5050 4625 
L 5050 3500 
L 5325 3500 
L 5650 3825 
L 6125 3350 
L 5050 3350 
L 5050 50 
Q 5050 -375 4500 -600 
Q 4500 -300 3600 0 
L 3600 125 
z
M 3100 2775 
L 3175 2825 
Q 3700 2400 3812 2175 
Q 3925 1950 3925 1800 
Q 3925 1625 3800 1462 
Q 3675 1300 3650 1300 
Q 3550 1300 3525 1650 
Q 3450 2125 3100 2775 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6bd4" d="M 1050 3400 
Q 1050 4525 1025 5075 
L 1625 4800 
L 1400 4600 
L 1400 2925 
L 2400 2925 
L 2700 3225 
L 3150 2775 
L 1400 2775 
L 1400 350 
L 3050 925 
L 3100 825 
Q 1625 150 1250 -250 
L 950 175 
Q 1050 275 1050 525 
L 1050 3400 
z
M 3425 3825 
Q 3425 4575 3400 5125 
L 4025 4825 
L 3775 4625 
L 3775 2525 
Q 4300 2925 4712 3400 
Q 5125 3875 5275 4175 
L 5775 3700 
Q 5475 3650 5025 3250 
Q 4550 2850 3775 2375 
L 3775 425 
Q 3775 125 4100 125 
L 5125 125 
Q 5325 125 5400 350 
Q 5475 575 5500 1425 
L 5625 1425 
Q 5625 825 5712 550 
Q 5800 275 6000 175 
Q 5875 -25 5712 -112 
Q 5550 -200 5275 -200 
L 3975 -200 
Q 3425 -200 3425 275 
L 3425 3825 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimSun-632f"/>
     <use xlink:href="#SimSun-52a8" x="100"/>
     <use xlink:href="#SimSun-4f4d" x="200"/>
     <use xlink:href="#SimSun-79fb" x="300"/>
     <use xlink:href="#SimSun-65f6" x="400"/>
     <use xlink:href="#SimSun-95f4" x="500"/>
     <use xlink:href="#SimSun-5e8f" x="600"/>
     <use xlink:href="#SimSun-5217" x="700"/>
     <use xlink:href="#SimSun-5bf9" x="800"/>
     <use xlink:href="#SimSun-6bd4" x="900"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="line2d_59">
     <path d="M 925.100391 391.629844 
L 935.600391 391.629844 
L 946.100391 391.629844 
" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_35">
     <!-- 真实振动位移 -->
     <g transform="translate(954.500391 395.304844)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-771f"/>
      <use xlink:href="#SimSun-5b9e" x="100"/>
      <use xlink:href="#SimSun-632f" x="200"/>
      <use xlink:href="#SimSun-52a8" x="300"/>
      <use xlink:href="#SimSun-4f4d" x="400"/>
      <use xlink:href="#SimSun-79fb" x="500"/>
     </g>
    </g>
    <g id="line2d_60">
     <path d="M 925.100391 406.518516 
L 935.600391 406.518516 
L 946.100391 406.518516 
" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
    </g>
    <g id="text_36">
     <!-- LSTM+卡尔曼滤波预测 -->
     <g transform="translate(954.500391 410.193516)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-5361" x="250"/>
      <use xlink:href="#SimSun-5c14" x="350"/>
      <use xlink:href="#SimSun-66fc" x="450"/>
      <use xlink:href="#SimSun-6ee4" x="550"/>
      <use xlink:href="#SimSun-6ce2" x="650"/>
      <use xlink:href="#SimSun-9884" x="750"/>
      <use xlink:href="#SimSun-6d4b" x="850"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pea463a64b3">
   <rect x="48.756641" y="23.191406" width="1014.94375" height="295.9525"/>
  </clipPath>
  <clipPath id="pfb01c432e2">
   <rect x="48.756641" y="377.487656" width="1014.94375" height="295.9525"/>
  </clipPath>
 </defs>
</svg>
