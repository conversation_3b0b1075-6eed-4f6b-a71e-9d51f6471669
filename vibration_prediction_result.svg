<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1070.810391pt" height="710.015156pt" viewBox="0 0 1070.810391 710.015156" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-08T10:42:57.578439</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 710.015156 
L 1070.810391 710.015156 
L 1070.810391 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 48.756641 319.143906 
L 1063.610391 319.143906 
L 1063.610391 23.191406 
L 48.756641 23.191406 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 94.886357 319.143906 
L 94.886357 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="ma73ec2b09a" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#ma73ec2b09a" x="94.886357" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g transform="translate(92.261357 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-30" d="M 1600 4225 
Q 1250 4225 1012 3687 
Q 775 3150 775 2250 
Q 775 1300 1012 775 
Q 1250 250 1600 250 
Q 1975 250 2187 775 
Q 2400 1300 2400 2250 
Q 2400 3150 2200 3687 
Q 2000 4225 1600 4225 
z
M 1600 50 
Q 1050 50 675 625 
Q 300 1200 300 2250 
Q 300 3225 662 3825 
Q 1025 4425 1600 4425 
Q 2150 4425 2512 3850 
Q 2875 3275 2875 2250 
Q 2875 1225 2512 637 
Q 2150 50 1600 50 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 210.49968 319.143906 
L 210.49968 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#ma73ec2b09a" x="210.49968" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 50 -->
      <g transform="translate(205.24968 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-35" d="M 1725 2850 
Q 2200 2850 2500 2487 
Q 2800 2125 2800 1525 
Q 2800 850 2487 450 
Q 2175 50 1525 50 
Q 1075 50 725 312 
Q 375 575 375 950 
Q 375 1100 462 1212 
Q 550 1325 700 1325 
Q 850 1325 900 1225 
Q 950 1125 950 1050 
Q 950 900 875 825 
Q 800 725 800 625 
Q 800 425 1037 325 
Q 1275 225 1550 225 
Q 1950 225 2162 550 
Q 2375 875 2375 1475 
Q 2375 1975 2187 2287 
Q 2000 2600 1625 2600 
Q 1350 2600 1150 2500 
Q 950 2400 775 2075 
L 550 2100 
L 675 4375 
L 2725 4375 
L 2650 4000 
L 850 4000 
L 750 2350 
Q 1000 2700 1237 2775 
Q 1475 2850 1725 2850 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-35"/>
       <use xlink:href="#SimSun-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 326.113003 319.143906 
L 326.113003 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#ma73ec2b09a" x="326.113003" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 100 -->
      <g transform="translate(318.238003 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-31" d="M 1825 4450 
L 1825 650 
Q 1825 450 1950 350 
Q 2075 250 2300 250 
L 2550 250 
L 2550 100 
L 725 100 
L 725 250 
L 950 250 
Q 1200 250 1312 350 
Q 1425 450 1425 650 
L 1425 3675 
Q 1425 3775 1362 3837 
Q 1300 3900 1175 3900 
L 725 3900 
L 725 4050 
L 950 4050 
Q 1250 4050 1437 4150 
Q 1625 4250 1725 4450 
L 1825 4450 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 441.726326 319.143906 
L 441.726326 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#ma73ec2b09a" x="441.726326" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 150 -->
      <g transform="translate(433.851326 333.649766)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-35" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 557.339649 319.143906 
L 557.339649 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#ma73ec2b09a" x="557.339649" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 200 -->
      <g transform="translate(549.464649 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-32" d="M 2325 3325 
Q 2325 3775 2125 4012 
Q 1925 4250 1525 4250 
Q 1225 4250 1012 4087 
Q 800 3925 800 3675 
Q 800 3525 900 3425 
Q 975 3325 975 3225 
Q 975 3100 912 3037 
Q 850 2975 725 2975 
Q 575 2975 487 3062 
Q 400 3150 400 3350 
Q 400 3875 775 4150 
Q 1150 4425 1575 4425 
Q 2175 4425 2462 4125 
Q 2750 3825 2750 3375 
Q 2750 3075 2612 2775 
Q 2475 2475 2175 2200 
Q 1450 1500 1062 1062 
Q 675 625 600 475 
L 2075 475 
Q 2300 475 2450 650 
Q 2600 825 2650 1175 
L 2800 1175 
L 2650 100 
L 325 100 
L 325 425 
Q 450 650 737 1000 
Q 1025 1350 1500 1825 
Q 1925 2250 2125 2625 
Q 2325 3000 2325 3325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-32"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 672.952972 319.143906 
L 672.952972 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#ma73ec2b09a" x="672.952972" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 250 -->
      <g transform="translate(665.077972 333.649766)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-32"/>
       <use xlink:href="#SimSun-35" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 788.566295 319.143906 
L 788.566295 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#ma73ec2b09a" x="788.566295" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 300 -->
      <g transform="translate(780.691295 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-33" d="M 2800 1250 
Q 2800 775 2450 412 
Q 2100 50 1475 50 
Q 1025 50 700 300 
Q 375 550 375 875 
Q 375 1025 475 1137 
Q 575 1250 675 1250 
Q 825 1250 887 1137 
Q 950 1025 950 950 
Q 950 825 900 750 
Q 850 650 850 575 
Q 850 425 1037 325 
Q 1225 225 1450 225 
Q 1900 225 2125 487 
Q 2350 750 2350 1300 
Q 2350 1750 2112 2025 
Q 1875 2300 1225 2300 
L 1225 2475 
Q 1725 2475 1962 2712 
Q 2200 2950 2200 3375 
Q 2200 3725 2012 3987 
Q 1825 4250 1425 4250 
Q 1250 4250 1050 4162 
Q 850 4075 850 3875 
Q 850 3675 900 3625 
Q 950 3575 950 3500 
Q 950 3375 900 3300 
Q 850 3225 725 3225 
Q 625 3225 537 3300 
Q 450 3375 450 3575 
Q 450 3950 775 4187 
Q 1100 4425 1525 4425 
Q 2025 4425 2325 4112 
Q 2625 3800 2625 3450 
Q 2625 3075 2437 2812 
Q 2250 2550 1850 2425 
Q 2400 2225 2600 1900 
Q 2800 1575 2800 1250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-33"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 904.179618 319.143906 
L 904.179618 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#ma73ec2b09a" x="904.179618" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 350 -->
      <g transform="translate(896.304618 333.649766)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-33"/>
       <use xlink:href="#SimSun-35" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 1019.792941 319.143906 
L 1019.792941 23.191406 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#ma73ec2b09a" x="1019.792941" y="319.143906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 400 -->
      <g transform="translate(1011.917941 333.649766)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-34" d="M 2300 575 
Q 2300 400 2400 325 
Q 2500 250 2675 250 
L 2900 250 
L 2900 100 
L 1250 100 
L 1250 250 
L 1525 250 
Q 1725 250 1812 325 
Q 1900 400 1900 575 
L 1900 1400 
L 225 1400 
L 225 1525 
L 2050 4425 
L 2300 4425 
L 2300 1550 
L 2975 1550 
L 2975 1400 
L 2300 1400 
L 2300 575 
z
M 1875 3800 
L 450 1550 
L 1900 1550 
L 1900 3800 
L 1875 3800 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-34"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- 时间步 -->
     <g transform="translate(540.433516 347.370469)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-65f6" d="M 4500 3600 
Q 4500 4575 4475 5225 
L 5100 4900 
L 4850 4725 
L 4850 3600 
L 5225 3600 
L 5550 3925 
L 6025 3450 
L 4850 3450 
L 4850 100 
Q 4850 -375 4300 -575 
Q 4275 -200 3475 -25 
L 3475 100 
Q 4150 0 4325 25 
Q 4500 50 4500 325 
L 4500 3450 
L 3200 3450 
Q 2875 3450 2600 3375 
L 2375 3600 
L 4500 3600 
z
M 2875 2825 
Q 3525 2375 3637 2200 
Q 3750 2025 3750 1925 
Q 3750 1800 3637 1637 
Q 3525 1475 3475 1475 
Q 3400 1475 3325 1775 
Q 3200 2225 2800 2750 
L 2875 2825 
z
M 925 4150 
L 925 2650 
L 1950 2650 
L 1950 4150 
L 925 4150 
z
M 925 2500 
L 925 900 
L 1950 900 
L 1950 2500 
L 925 2500 
z
M 2300 2000 
Q 2300 800 2325 450 
L 1950 250 
L 1950 750 
L 925 750 
L 925 225 
L 550 25 
Q 575 825 575 2425 
Q 575 4025 550 4500 
L 950 4300 
L 1900 4300 
L 2100 4550 
L 2500 4225 
L 2300 4050 
L 2300 2000 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-95f4" d="M 2150 525 
Q 2175 1325 2175 2112 
Q 2175 2900 2150 3675 
L 2525 3450 
L 3750 3450 
L 4000 3700 
L 4325 3375 
L 4125 3200 
Q 4125 1550 4150 775 
L 3800 575 
L 3800 975 
L 2500 975 
L 2500 700 
L 2150 525 
z
M 2500 3300 
L 2500 2325 
L 3800 2325 
L 3800 3300 
L 2500 3300 
z
M 2500 2175 
L 2500 1125 
L 3800 1125 
L 3800 2175 
L 2500 2175 
z
M 1275 5200 
Q 1850 4825 1950 4675 
Q 2050 4525 2050 4425 
Q 2050 4275 1950 4137 
Q 1850 4000 1825 4000 
Q 1750 4000 1675 4275 
Q 1550 4650 1200 5125 
L 1275 5200 
z
M 1125 425 
Q 1125 25 1150 -375 
L 750 -525 
Q 775 -150 775 1950 
Q 775 4050 750 4425 
L 1350 4125 
L 1125 3975 
L 1125 425 
z
M 4225 125 
Q 5025 0 5125 37 
Q 5225 75 5225 250 
L 5225 4450 
L 3225 4450 
Q 2900 4450 2625 4375 
L 2400 4600 
L 5175 4600 
L 5425 4850 
L 5775 4500 
L 5575 4350 
L 5575 225 
Q 5575 -100 5462 -250 
Q 5350 -400 5000 -550 
Q 4800 -125 4225 25 
L 4225 125 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6b65" d="M 3125 2975 
Q 3125 4700 3100 5275 
L 3700 5000 
L 3475 4825 
L 3475 4200 
L 4800 4200 
L 5100 4500 
L 5550 4050 
L 3475 4050 
L 3475 2975 
L 5175 2975 
L 5550 3350 
L 6075 2825 
L 1150 2825 
Q 825 2825 550 2750 
L 325 2975 
L 1500 2975 
Q 1500 4225 1475 4650 
L 2075 4375 
L 1850 4200 
L 1850 2975 
L 3125 2975 
z
M 3100 650 
Q 3125 950 3125 1737 
Q 3125 2525 3100 2775 
L 3675 2500 
L 3475 2325 
Q 3475 1175 3500 800 
L 3100 650 
z
M 1850 2300 
L 2350 1925 
L 2075 1825 
Q 1275 750 550 250 
L 500 325 
Q 975 800 1350 1387 
Q 1725 1975 1850 2300 
z
M 5250 2275 
L 5700 1825 
L 5475 1775 
Q 3950 325 2875 -62 
Q 1800 -450 375 -650 
L 375 -550 
Q 1125 -425 2075 -112 
Q 3025 200 3862 837 
Q 4700 1475 5250 2275 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-65f6"/>
      <use xlink:href="#SimSun-95f4" x="100"/>
      <use xlink:href="#SimSun-6b65" x="200"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 48.756641 305.55419 
L 1063.610391 305.55419 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mdadd275ed6" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="305.55419" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- -0.4 -->
      <g transform="translate(20.756641 309.30712)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-2d" d="M 200 2225 
L 200 2450 
L 2975 2450 
L 2975 2225 
L 200 2225 
z
" transform="scale(0.015625)"/>
        <path id="SimSun-2e" d="M 800 25 
Q 650 25 537 125 
Q 425 225 425 400 
Q 425 575 537 675 
Q 650 775 800 775 
Q 950 775 1062 662 
Q 1175 550 1175 400 
Q 1175 225 1062 125 
Q 950 25 800 25 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-34" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 48.756641 245.907538 
L 1063.610391 245.907538 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="245.907538" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- -0.2 -->
      <g transform="translate(20.756641 249.660468)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 48.756641 186.260887 
L 1063.610391 186.260887 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="186.260887" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.0 -->
      <g transform="translate(26.006641 190.013817)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 48.756641 126.614235 
L 1063.610391 126.614235 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="126.614235" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0.2 -->
      <g transform="translate(26.006641 130.367165)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 48.756641 66.967584 
L 1063.610391 66.967584 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="66.967584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0.4 -->
      <g transform="translate(26.006641 70.720514)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_16">
     <!-- 位移 (μm) -->
     <g transform="translate(15.403125 197.417656)rotate(-90)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-4f4d" d="M 1250 -600 
Q 1275 150 1275 3100 
Q 750 2225 325 1775 
L 250 1825 
Q 725 2575 1112 3450 
Q 1500 4325 1700 5225 
L 2275 4925 
L 2050 4800 
Q 1650 3900 1475 3500 
L 1800 3275 
L 1600 3125 
Q 1600 125 1625 -400 
L 1250 -600 
z
M 3300 5125 
L 3375 5175 
Q 4100 4625 4112 4375 
Q 4125 4125 4000 4025 
Q 3875 3925 3850 3925 
Q 3725 3925 3700 4200 
Q 3625 4575 3300 5125 
z
M 2025 3725 
L 5050 3725 
L 5400 4075 
L 5900 3575 
L 2825 3575 
Q 2525 3575 2250 3500 
L 2025 3725 
z
M 2600 3000 
L 2675 3050 
Q 3300 2050 3400 1712 
Q 3500 1375 3500 1200 
Q 3500 950 3362 800 
Q 3225 650 3175 650 
Q 3100 650 3100 825 
Q 3075 1125 2975 1675 
Q 2875 2225 2600 3000 
z
M 4725 3325 
L 5325 3000 
Q 5100 2875 4875 2125 
Q 4650 1375 4175 -25 
L 5300 -25 
L 5650 325 
L 6150 -175 
L 2700 -175 
Q 2375 -175 2100 -250 
L 1875 -25 
L 4000 -25 
Q 4300 1000 4500 1962 
Q 4700 2925 4725 3325 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-79fb" d="M 4000 5150 
L 4450 4825 
Q 4275 4775 3875 4300 
L 5225 4300 
L 5425 4525 
L 5800 4150 
L 5575 4050 
Q 5175 3575 4850 3250 
Q 4525 2925 4050 2600 
Q 3575 2275 2825 2000 
L 2775 2075 
Q 3175 2275 3562 2537 
Q 3950 2800 4375 3200 
Q 4800 3600 5225 4150 
L 3800 4150 
L 3625 4000 
Q 3950 3850 4100 3712 
Q 4250 3575 4250 3475 
Q 4250 3375 4162 3250 
Q 4075 3125 4025 3125 
Q 3975 3125 3925 3300 
Q 3825 3600 3575 3925 
Q 3250 3600 2875 3325 
L 2825 3400 
Q 3250 3825 3537 4262 
Q 3825 4700 4000 5150 
z
M 4525 2775 
L 4950 2400 
Q 4650 2275 4275 1900 
L 5475 1900 
L 5675 2125 
L 6050 1750 
L 5850 1650 
Q 5600 1275 5175 787 
Q 4750 300 4012 -62 
Q 3275 -425 2075 -575 
L 2075 -500 
Q 2875 -325 3462 -62 
Q 4050 200 4537 600 
Q 5025 1000 5475 1750 
L 4175 1750 
L 3700 1375 
Q 4075 1150 4162 1012 
Q 4250 875 4250 775 
Q 4250 650 4150 537 
Q 4050 425 4025 425 
Q 3950 425 3925 650 
Q 3850 975 3625 1300 
Q 3025 875 2625 675 
L 2575 750 
Q 3025 1050 3625 1650 
Q 4225 2250 4525 2775 
z
M 475 4375 
Q 1075 4475 1625 4662 
Q 2175 4850 2400 5025 
L 2750 4600 
Q 2375 4550 1875 4475 
L 1875 3225 
L 2275 3225 
L 2550 3500 
L 2950 3075 
L 1875 3075 
L 1875 2525 
Q 2300 2350 2500 2187 
Q 2700 2025 2700 1875 
Q 2700 1750 2637 1625 
Q 2575 1500 2525 1500 
Q 2450 1500 2375 1700 
Q 2250 2000 1875 2400 
Q 1875 250 1900 -400 
L 1500 -600 
Q 1525 350 1525 2200 
Q 1000 1125 350 500 
L 300 550 
Q 725 1175 1037 1850 
Q 1350 2525 1500 3075 
L 1100 3075 
Q 775 3075 500 3000 
L 275 3225 
L 1550 3225 
L 1550 4400 
Q 800 4300 475 4275 
L 475 4375 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-20" d="M 0 0 
Q 0 0 0 0 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-28" d="M 2900 5025 
Q 2325 4425 2037 3737 
Q 1750 3050 1750 2300 
Q 1750 1525 2037 850 
Q 2325 175 2900 -425 
L 2775 -550 
Q 2075 75 1725 787 
Q 1375 1500 1375 2300 
Q 1375 3075 1725 3787 
Q 2075 4500 2775 5150 
L 2900 5025 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-3bc" d="M 2575 900 
L 2575 250 
Q 2575 50 2950 75 
L 2950 -75 
L 1825 -75 
L 1825 75 
Q 2200 50 2200 250 
L 2200 3000 
Q 2225 3175 1950 3175 
L 1950 3300 
L 2575 3300 
L 2575 1300 
Q 2650 950 3112 950 
Q 3575 950 3850 1425 
L 3850 3025 
Q 3850 3175 3650 3175 
L 3650 3300 
L 4225 3300 
L 4225 1375 
Q 4200 1000 4325 1025 
Q 4450 1050 4575 1150 
Q 4700 1250 4737 1225 
Q 4775 1200 4587 912 
Q 4400 625 4125 687 
Q 3850 750 3850 1075 
Q 3400 600 3037 650 
Q 2675 700 2575 900 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6d" d="M 175 2900 
Q 300 2900 425 2925 
Q 550 2950 625 3000 
L 675 3000 
L 675 2575 
Q 800 2775 950 2862 
Q 1100 2950 1275 2950 
Q 1500 2950 1625 2862 
Q 1750 2775 1775 2575 
Q 1900 2775 2062 2862 
Q 2225 2950 2400 2950 
Q 2700 2950 2812 2775 
Q 2925 2600 2925 2325 
L 2925 425 
Q 2925 325 2975 287 
Q 3025 250 3125 250 
L 3175 250 
L 3175 100 
L 2325 100 
L 2325 250 
L 2375 250 
Q 2475 250 2525 287 
Q 2575 325 2575 425 
L 2575 2300 
Q 2575 2575 2512 2662 
Q 2450 2750 2325 2750 
Q 2150 2750 2012 2625 
Q 1875 2500 1800 2275 
L 1800 425 
Q 1800 325 1850 287 
Q 1900 250 2000 250 
L 2050 250 
L 2050 100 
L 1200 100 
L 1200 250 
L 1250 250 
Q 1350 250 1400 287 
Q 1450 325 1450 425 
L 1450 2300 
Q 1450 2525 1400 2637 
Q 1350 2750 1200 2750 
Q 1025 2750 887 2625 
Q 750 2500 675 2250 
L 675 425 
Q 675 325 725 287 
Q 775 250 875 250 
L 925 250 
L 925 100 
L 75 100 
L 75 250 
L 125 250 
Q 225 250 275 287 
Q 325 325 325 425 
L 325 2600 
Q 325 2675 300 2712 
Q 275 2750 200 2750 
L 100 2750 
L 100 2900 
L 175 2900 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-29" d="M 275 5025 
L 400 5150 
Q 1100 4500 1450 3787 
Q 1800 3075 1800 2300 
Q 1800 1500 1450 787 
Q 1100 75 400 -550 
L 275 -425 
Q 850 175 1137 850 
Q 1425 1525 1425 2300 
Q 1425 3050 1137 3737 
Q 850 4425 275 5025 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4f4d"/>
      <use xlink:href="#SimSun-79fb" x="100"/>
      <use xlink:href="#SimSun-20" x="200"/>
      <use xlink:href="#SimSun-28" x="250"/>
      <use xlink:href="#SimSun-3bc" x="300"/>
      <use xlink:href="#SimSun-6d" x="400"/>
      <use xlink:href="#SimSun-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="line2d_29">
    <path d="M 94.886357 199.204916 
L 97.198623 191.871587 
L 101.823156 181.470277 
L 104.135422 174.804825 
L 106.447689 164.821335 
L 108.759955 151.824337 
L 113.384488 122.306602 
L 115.696755 110.100154 
L 118.009021 101.454793 
L 120.321288 95.511122 
L 124.945821 86.810654 
L 127.258087 82.039446 
L 129.570353 76.82144 
L 131.88262 71.911827 
L 134.194886 67.447637 
L 138.819419 59.281328 
L 143.443952 50.535718 
L 145.756219 47.226982 
L 148.068485 45.815477 
L 150.380752 46.794581 
L 152.693018 49.887464 
L 155.005285 54.134899 
L 159.629817 63.323904 
L 164.25435 72.459245 
L 171.19115 87.518537 
L 175.815683 96.794982 
L 178.127949 102.416811 
L 180.440216 109.713788 
L 182.752482 119.496309 
L 185.064749 131.11996 
L 187.377015 144.010606 
L 189.689281 156.202922 
L 192.001548 166.829192 
L 194.313814 174.781375 
L 196.626081 180.641509 
L 198.938347 185.284119 
L 201.250614 190.485435 
L 203.56288 197.40735 
L 205.875147 206.992017 
L 208.187413 218.518884 
L 210.49968 231.275939 
L 212.811946 243.288614 
L 215.124213 253.828067 
L 217.436479 261.954338 
L 219.748745 268.184613 
L 222.061012 272.866741 
L 224.373278 276.836903 
L 228.997811 283.641447 
L 231.310078 286.605926 
L 233.622344 289.304924 
L 235.934611 291.591001 
L 238.246877 293.481939 
L 240.559144 294.781283 
L 242.87141 295.440226 
L 245.183677 295.458007 
L 247.495943 295.015985 
L 249.808209 294.343882 
L 252.120476 293.478869 
L 254.432742 292.275206 
L 256.745009 290.364339 
L 259.057275 287.646938 
L 261.369542 284.185849 
L 263.681808 280.502798 
L 268.306341 273.654317 
L 270.618608 269.652854 
L 272.930874 263.791607 
L 275.243141 255.487897 
L 277.555407 244.414257 
L 282.17994 219.292538 
L 284.492206 208.261081 
L 286.804473 199.211873 
L 289.116739 192.424272 
L 291.429006 186.95278 
L 296.053539 177.240355 
L 298.365805 171.679632 
L 300.678072 164.766474 
L 302.990338 156.410866 
L 305.302605 146.290366 
L 309.927137 124.619817 
L 312.239404 115.577331 
L 314.55167 108.781619 
L 316.863937 104.440651 
L 319.176203 101.508769 
L 321.48847 99.03395 
L 326.113003 93.37367 
L 328.425269 90.89657 
L 330.737536 89.505497 
L 333.049802 89.257729 
L 335.362069 89.771946 
L 339.986601 91.271492 
L 342.298868 92.262121 
L 344.611134 93.884966 
L 346.923401 96.420348 
L 349.235667 99.547944 
L 356.172467 109.89345 
L 358.484733 114.763799 
L 360.797 121.479276 
L 363.109266 130.429104 
L 367.733799 150.516266 
L 370.046065 158.929632 
L 372.358332 165.473179 
L 374.670598 170.256759 
L 379.295131 178.657747 
L 383.919664 188.437387 
L 386.231931 193.344417 
L 390.856464 202.274463 
L 393.16873 207.047646 
L 395.480997 212.723108 
L 397.793263 219.050593 
L 400.105529 225.814429 
L 402.417796 232.077083 
L 404.730062 237.529544 
L 407.042329 241.729918 
L 409.354595 244.952443 
L 411.666862 247.33016 
L 413.979128 249.203557 
L 416.291395 250.615589 
L 418.603661 251.649346 
L 420.915928 252.284595 
L 423.228194 252.63681 
L 427.852727 253.077507 
L 432.47726 253.833484 
L 434.789526 254.219779 
L 437.101793 254.453272 
L 439.414059 254.444473 
L 441.726326 254.187476 
L 444.038592 253.728494 
L 446.350859 253.051425 
L 448.663125 252.085516 
L 450.975392 250.578898 
L 453.287658 248.291342 
L 455.599925 245.126578 
L 457.912191 240.976313 
L 460.224457 236.23285 
L 464.84899 226.057258 
L 467.161257 221.255001 
L 469.473523 216.977873 
L 471.78579 213.077777 
L 474.098056 209.702875 
L 476.410323 206.70381 
L 478.722589 204.166205 
L 481.034856 201.889955 
L 485.659389 197.668431 
L 487.971655 195.450416 
L 494.908454 188.3607 
L 499.532987 183.818205 
L 501.845254 181.46012 
L 508.782053 173.87016 
L 513.406586 169.36096 
L 518.031119 164.999629 
L 522.655652 160.256893 
L 524.967918 157.816438 
L 527.280185 155.1892 
L 529.592451 152.261916 
L 531.904718 148.635536 
L 534.216984 144.292178 
L 538.841517 133.749487 
L 541.153784 128.3934 
L 543.46605 123.706772 
L 545.778317 119.680911 
L 548.090583 116.32696 
L 550.402849 113.459229 
L 552.715116 110.768999 
L 555.027382 108.351044 
L 557.339649 106.265258 
L 559.651915 104.790139 
L 561.964182 103.951892 
L 564.276448 103.691031 
L 566.588715 103.787963 
L 568.900981 104.075803 
L 571.213248 104.56676 
L 573.525514 105.402993 
L 575.837781 106.811833 
L 578.150047 108.778435 
L 587.399113 118.378534 
L 589.711379 121.124823 
L 592.023646 124.694074 
L 594.335912 129.533709 
L 596.648179 135.307157 
L 598.960445 141.68245 
L 601.272712 147.717204 
L 603.584978 153.174458 
L 605.897245 157.783402 
L 612.834044 170.395607 
L 617.458577 178.52934 
L 619.770843 182.02735 
L 624.395376 188.565782 
L 629.019909 195.82338 
L 631.332176 199.542732 
L 633.644442 202.86508 
L 638.268975 208.920888 
L 640.581241 212.55866 
L 642.893508 217.215495 
L 645.205774 222.82066 
L 652.142574 241.78074 
L 654.45484 247.123833 
L 656.767107 251.841077 
L 659.079373 255.799722 
L 661.39164 259.210813 
L 663.703906 261.950772 
L 666.016173 264.147713 
L 668.328439 265.759891 
L 670.640705 266.901397 
L 672.952972 267.520116 
L 675.265238 267.571528 
L 677.577505 266.990491 
L 679.889771 265.777379 
L 682.202038 264.087564 
L 684.514304 261.917261 
L 686.826571 259.208178 
L 689.138837 255.440474 
L 691.451104 250.290102 
L 693.76337 243.321958 
L 696.075637 235.118964 
L 698.387903 226.182634 
L 700.700169 217.861281 
L 703.012436 210.539566 
L 705.324702 204.640688 
L 707.636969 199.539438 
L 719.198301 176.541636 
L 721.510568 170.824632 
L 723.822834 163.413019 
L 726.135101 153.512493 
L 728.447367 141.881411 
L 730.759633 129.292058 
L 733.0719 117.825545 
L 735.384166 108.271928 
L 737.696433 101.250544 
L 740.008699 96.275984 
L 744.633232 88.022348 
L 749.257765 78.753932 
L 751.570032 74.017095 
L 758.506831 60.948665 
L 760.819097 56.093504 
L 765.44363 45.756609 
L 767.755897 41.261789 
L 770.068163 38.000376 
L 772.38043 36.643793 
L 774.692696 37.403077 
L 777.004963 40.166798 
L 779.317229 44.660163 
L 781.629496 50.161825 
L 788.566295 68.732833 
L 793.190828 80.011333 
L 797.815361 90.075122 
L 800.127627 95.617457 
L 802.439894 102.852467 
L 804.75216 112.322692 
L 807.064427 124.624389 
L 811.68896 152.873333 
L 814.001226 165.313121 
L 816.313493 175.0527 
L 818.625759 181.664148 
L 823.250292 191.613113 
L 825.562558 198.424616 
L 827.874825 207.466899 
L 830.187091 218.655828 
L 832.499358 230.591386 
L 834.811624 241.689452 
L 837.123891 251.465865 
L 839.436157 259.221246 
L 841.748424 265.463349 
L 844.06069 270.369586 
L 846.372957 274.597531 
L 850.997489 281.939978 
L 855.622022 288.758502 
L 857.934289 291.796386 
L 860.246555 294.445441 
L 862.558822 296.476429 
L 864.871088 297.938438 
L 867.183355 298.808125 
L 869.495621 299.162099 
L 871.807888 298.986494 
L 874.120154 298.246135 
L 876.432421 296.966246 
L 878.744687 295.147297 
L 881.056953 292.948206 
L 883.36922 290.351038 
L 885.681486 287.493015 
L 890.306019 280.991389 
L 897.242819 270.722628 
L 899.555085 266.419288 
L 901.867352 260.385159 
L 904.179618 252.250928 
L 906.491885 241.683111 
L 911.116417 217.709958 
L 913.428684 206.997653 
L 915.74095 198.13571 
L 918.053217 191.508732 
L 920.365483 186.207532 
L 922.67775 181.537563 
L 924.990016 176.448794 
L 927.302283 170.416089 
L 929.614549 163.238547 
L 931.926816 154.509585 
L 934.239082 144.774679 
L 936.551349 134.253598 
L 938.863615 124.326399 
L 941.175881 115.635105 
L 943.488148 109.155119 
L 945.800414 104.52362 
L 950.424947 97.881032 
L 952.737214 94.179233 
L 957.361747 85.764631 
L 959.674013 82.16138 
L 961.98628 79.396759 
L 968.923079 72.243311 
L 971.235345 70.120949 
L 973.547612 68.935164 
L 975.859878 69.128661 
L 978.172145 70.503328 
L 982.796678 74.084776 
L 985.108944 75.599561 
L 987.421211 77.483647 
L 989.733477 80.316005 
L 992.045744 84.381682 
L 994.35801 88.941748 
L 996.670277 93.228344 
L 1001.294809 99.755558 
L 1003.607076 104.13438 
L 1005.919342 111.542459 
L 1008.231609 122.205592 
L 1012.856142 147.911882 
L 1015.168408 157.657407 
L 1017.480675 162.715761 
L 1017.480675 162.715761 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_30">
    <path d="M 94.886357 199.204916 
L 97.198623 194.996828 
L 99.510889 184.093997 
L 101.823156 190.867034 
L 104.135422 176.597022 
L 106.447689 161.390158 
L 108.759955 149.519455 
L 111.072222 140.005419 
L 113.384488 129.275524 
L 115.696755 115.511899 
L 118.009021 109.675517 
L 120.321288 108.810482 
L 122.633554 116.161236 
L 124.945821 103.772678 
L 127.258087 112.167641 
L 129.570353 91.975308 
L 131.88262 106.904385 
L 134.194886 76.145899 
L 136.507153 71.600586 
L 138.819419 71.835109 
L 141.131686 70.752217 
L 143.443952 67.220042 
L 145.756219 63.220455 
L 148.068485 82.173641 
L 150.380752 93.226228 
L 152.693018 69.558567 
L 155.005285 93.694743 
L 157.317551 85.516413 
L 159.629817 71.668054 
L 161.942084 83.719217 
L 164.25435 84.413078 
L 166.566617 92.73617 
L 168.878883 107.242619 
L 171.19115 102.773221 
L 173.503416 109.017048 
L 175.815683 104.598988 
L 178.127949 104.953437 
L 180.440216 115.925107 
L 182.752482 123.78311 
L 185.064749 143.546518 
L 187.377015 136.983226 
L 189.689281 157.825038 
L 192.001548 169.757338 
L 194.313814 166.893225 
L 196.626081 176.415261 
L 198.938347 181.368011 
L 201.250614 182.996302 
L 203.56288 180.964282 
L 205.875147 199.362869 
L 208.187413 206.741562 
L 210.49968 213.372635 
L 212.811946 228.769477 
L 215.124213 239.078853 
L 217.436479 259.786256 
L 219.748745 259.222264 
L 222.061012 260.787372 
L 224.373278 253.901178 
L 226.685545 262.560268 
L 228.997811 256.865464 
L 231.310078 260.093728 
L 233.622344 263.713667 
L 235.934611 264.898065 
L 238.246877 270.71682 
L 240.559144 270.177831 
L 242.87141 305.69152 
L 245.183677 266.186549 
L 247.495943 294.298883 
L 249.808209 273.862423 
L 252.120476 264.093267 
L 254.432742 265.614056 
L 256.745009 262.363355 
L 259.057275 261.74424 
L 261.369542 259.733727 
L 263.681808 258.977297 
L 265.994075 253.086136 
L 268.306341 284.916484 
L 270.618608 249.382858 
L 272.930874 265.249638 
L 275.243141 247.071735 
L 277.555407 220.073381 
L 279.867673 217.733383 
L 282.17994 203.639469 
L 284.492206 198.555321 
L 286.804473 196.183963 
L 289.116739 189.582569 
L 291.429006 185.147053 
L 293.741272 183.244794 
L 296.053539 181.706289 
L 298.365805 179.565685 
L 300.678072 171.482642 
L 302.990338 145.888278 
L 305.302605 146.671594 
L 307.614871 134.31365 
L 309.927137 128.67056 
L 312.239404 120.103055 
L 314.55167 117.677566 
L 316.863937 143.77337 
L 319.176203 112.755568 
L 321.48847 126.720839 
L 323.800736 117.964171 
L 326.113003 95.103441 
L 328.425269 102.6739 
L 330.737536 100.759805 
L 333.049802 99.437189 
L 335.362069 112.26813 
L 337.674335 103.38677 
L 339.986601 107.765885 
L 342.298868 108.311244 
L 344.611134 96.199258 
L 346.923401 103.84867 
L 349.235667 122.437023 
L 351.547934 100.937712 
L 353.8602 114.794752 
L 356.172467 116.594473 
L 358.484733 125.185295 
L 360.797 129.664887 
L 363.109266 147.592395 
L 365.421533 148.737918 
L 367.733799 169.926602 
L 370.046065 150.532441 
L 372.358332 160.916576 
L 374.670598 165.306933 
L 376.982865 166.390986 
L 379.295131 173.694179 
L 381.607398 175.663084 
L 383.919664 187.721759 
L 386.231931 219.764665 
L 388.544197 189.950978 
L 390.856464 215.53914 
L 393.16873 206.463306 
L 395.480997 196.46779 
L 397.793263 204.78557 
L 400.105529 211.934116 
L 402.417796 218.1155 
L 404.730062 232.34078 
L 407.042329 230.058439 
L 409.354595 236.903574 
L 411.666862 231.270154 
L 413.979128 224.081723 
L 416.291395 235.60147 
L 418.603661 227.85471 
L 420.915928 236.331993 
L 423.228194 232.810304 
L 425.540461 241.130324 
L 427.852727 229.980539 
L 430.164993 255.370726 
L 432.47726 242.597717 
L 434.789526 253.016192 
L 437.101793 236.145466 
L 439.414059 228.195137 
L 441.726326 229.325487 
L 444.038592 246.523975 
L 446.350859 235.700356 
L 448.663125 223.840857 
L 450.975392 237.281044 
L 453.287658 250.062994 
L 455.599925 239.887508 
L 457.912191 239.846226 
L 460.224457 234.760192 
L 462.536724 220.269385 
L 464.84899 204.916635 
L 467.161257 207.704709 
L 469.473523 200.94211 
L 471.78579 209.511914 
L 474.098056 190.199471 
L 476.410323 212.781574 
L 478.722589 183.694394 
L 481.034856 234.955159 
L 483.347122 185.299641 
L 485.659389 211.591924 
L 487.971655 190.079831 
L 490.283921 176.364033 
L 492.596188 180.187506 
L 494.908454 178.360924 
L 497.220721 182.20622 
L 499.532987 178.127224 
L 501.845254 177.17324 
L 504.15752 182.598035 
L 506.469787 199.669148 
L 508.782053 169.770394 
L 511.09432 192.880394 
L 513.406586 173.07358 
L 515.718853 156.372904 
L 518.031119 161.766822 
L 520.343385 156.784763 
L 522.655652 158.338036 
L 524.967918 156.148552 
L 527.280185 151.016817 
L 529.592451 176.683089 
L 531.904718 165.823092 
L 534.216984 156.469279 
L 536.529251 159.539291 
L 538.841517 146.53171 
L 541.153784 125.658677 
L 543.46605 129.033264 
L 545.778317 122.490443 
L 548.090583 124.6234 
L 550.402849 131.661867 
L 552.715116 120.17784 
L 555.027382 130.02575 
L 557.339649 117.098917 
L 559.651915 120.929732 
L 561.964182 100.165858 
L 564.276448 117.927743 
L 566.588715 106.915311 
L 568.900981 108.202485 
L 571.213248 106.719367 
L 573.525514 116.707675 
L 575.837781 123.070705 
L 578.150047 135.446702 
L 580.462313 121.789745 
L 582.77458 144.262176 
L 585.086846 121.348954 
L 587.399113 119.44747 
L 589.711379 125.052658 
L 592.023646 124.942606 
L 594.335912 132.987914 
L 596.648179 138.102185 
L 598.960445 132.952236 
L 601.272712 183.365876 
L 603.584978 159.207022 
L 605.897245 175.963448 
L 608.209511 172.291094 
L 610.521777 166.266999 
L 612.834044 164.107844 
L 615.14631 167.528116 
L 617.458577 179.740573 
L 619.770843 184.175316 
L 622.08311 186.745927 
L 624.395376 191.229714 
L 626.707643 187.678032 
L 629.019909 185.1019 
L 631.332176 203.3566 
L 633.644442 184.526731 
L 635.956709 208.150087 
L 638.268975 190.147046 
L 640.581241 201.775835 
L 642.893508 204.178367 
L 645.205774 216.001968 
L 647.518041 210.414389 
L 649.830307 246.105582 
L 652.142574 217.822034 
L 654.45484 238.639361 
L 656.767107 227.676966 
L 659.079373 235.73864 
L 661.39164 236.984036 
L 663.703906 242.70123 
L 666.016173 239.776923 
L 668.328439 247.633798 
L 670.640705 268.399035 
L 672.952972 254.854518 
L 675.265238 265.139514 
L 677.577505 260.725118 
L 679.889771 241.124798 
L 682.202038 243.083472 
L 684.514304 239.072118 
L 686.826571 238.146309 
L 689.138837 237.539972 
L 691.451104 236.276349 
L 693.76337 222.180154 
L 696.075637 245.48922 
L 698.387903 221.381634 
L 700.700169 220.48928 
L 703.012436 215.622833 
L 705.324702 188.40537 
L 707.636969 192.02624 
L 709.949235 184.596661 
L 712.261502 182.168695 
L 714.573768 191.849101 
L 716.886035 178.28053 
L 719.198301 179.036697 
L 721.510568 175.621634 
L 723.822834 151.689479 
L 726.135101 150.967897 
L 728.447367 142.265709 
L 730.759633 126.927055 
L 733.0719 123.974923 
L 735.384166 115.306608 
L 737.696433 99.188192 
L 740.008699 142.547333 
L 742.320966 102.558467 
L 744.633232 118.064587 
L 746.945499 106.159003 
L 749.257765 83.829843 
L 751.570032 87.076088 
L 753.882298 86.260162 
L 756.194565 79.727881 
L 758.506831 87.49355 
L 760.819097 79.118042 
L 763.131364 74.701107 
L 765.44363 76.455019 
L 767.755897 59.206945 
L 770.068163 55.862752 
L 772.38043 62.175967 
L 774.692696 56.940482 
L 777.004963 58.991844 
L 779.317229 62.993804 
L 781.629496 78.281952 
L 783.941762 69.986121 
L 786.254029 101.487974 
L 788.566295 81.951612 
L 790.878561 112.64434 
L 793.190828 91.762533 
L 795.503094 91.579561 
L 797.815361 100.623212 
L 800.127627 100.319561 
L 802.439894 112.266725 
L 804.75216 118.351394 
L 807.064427 119.858092 
L 809.376693 171.859821 
L 811.68896 158.21552 
L 814.001226 184.298854 
L 816.313493 179.768923 
L 818.625759 180.156806 
L 820.938025 175.177468 
L 823.250292 182.705316 
L 827.874825 202.297522 
L 830.187091 210.2637 
L 832.499358 228.398465 
L 834.811624 219.623862 
L 837.123891 239.150364 
L 839.436157 244.2367 
L 841.748424 253.808886 
L 844.06069 250.891062 
L 846.372957 246.162773 
L 848.685223 255.546424 
L 850.997489 254.848333 
L 853.309756 261.601126 
L 855.622022 265.91175 
L 857.934289 291.941876 
L 860.246555 288.345912 
L 862.558822 272.46923 
L 864.871088 291.029231 
L 867.183355 265.767646 
L 869.495621 271.292496 
L 871.807888 271.666474 
L 874.120154 269.105106 
L 876.432421 273.705279 
L 878.744687 273.931831 
L 881.056953 269.692779 
L 883.36922 279.789605 
L 885.681486 255.095174 
L 887.993753 272.1334 
L 890.306019 258.031013 
L 892.618286 247.082843 
L 894.930552 255.496854 
L 897.242819 245.351007 
L 899.555085 243.745899 
L 901.867352 243.04075 
L 904.179618 237.922153 
L 906.491885 232.240913 
L 908.804151 223.060047 
L 911.116417 215.246334 
L 913.428684 204.797511 
L 915.74095 181.88774 
L 918.053217 183.860152 
L 920.365483 176.450589 
L 922.67775 176.836333 
L 924.990016 167.42231 
L 927.302283 164.058189 
L 929.614549 187.40478 
L 931.926816 161.51553 
L 934.239082 164.841744 
L 941.175881 117.383604 
L 943.488148 114.89703 
L 945.800414 109.127812 
L 948.112681 109.986751 
L 950.424947 103.297919 
L 952.737214 95.549345 
L 955.04948 132.404715 
L 957.361747 104.114021 
L 959.674013 113.002093 
L 961.98628 105.931867 
L 964.298546 89.016495 
L 966.610813 88.093357 
L 968.923079 84.775293 
L 971.235345 84.017658 
L 973.547612 94.853252 
L 975.859878 87.429752 
L 978.172145 88.975349 
L 980.484411 95.232593 
L 982.796678 75.28169 
L 985.108944 82.3275 
L 987.421211 102.701527 
L 989.733477 83.104953 
L 992.045744 99.616643 
L 994.35801 96.981682 
L 996.670277 123.309973 
L 998.982543 105.277154 
L 1001.294809 134.686794 
L 1003.607076 108.791657 
L 1005.919342 143.487823 
L 1008.231609 124.198493 
L 1010.543875 131.601751 
L 1012.856142 146.540506 
L 1015.168408 152.800333 
L 1017.480675 159.410974 
L 1017.480675 159.410974 
" clip-path="url(#p5040f8f6e3)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
   </g>
   <g id="patch_3">
    <path d="M 48.756641 319.143906 
L 48.756641 23.191406 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 1063.610391 319.143906 
L 1063.610391 23.191406 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 48.756641 319.143906 
L 1063.610391 319.143906 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 48.756641 23.191406 
L 1063.610391 23.191406 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_17">
    <!-- 振动数据预测结果 -->
    <g transform="translate(505.783516 17.191406)scale(0.126 -0.126)">
     <defs>
      <path id="SimSun-632f" d="M 400 100 
Q 950 25 1125 25 
Q 1300 25 1300 325 
L 1300 2000 
Q 1025 1875 650 1650 
L 525 1475 
L 225 1925 
Q 675 2050 1300 2275 
L 1300 3550 
L 700 3550 
L 475 3500 
L 275 3700 
L 1300 3700 
Q 1300 4700 1275 5200 
L 1875 4925 
L 1650 4750 
L 1650 3700 
L 1925 3700 
L 2200 3975 
L 2625 3550 
L 1650 3550 
L 1650 2400 
L 2475 2725 
L 2500 2650 
L 1650 2175 
L 1650 150 
Q 1675 -325 1125 -550 
Q 1125 -225 400 0 
L 400 100 
z
M 3750 150 
L 4525 550 
L 4575 475 
Q 3775 -125 3625 -475 
L 3325 -75 
Q 3450 100 3425 350 
L 3425 2450 
L 3075 2450 
Q 2975 350 1875 -625 
L 1800 -575 
Q 2250 0 2487 837 
Q 2725 1675 2725 2937 
Q 2725 4200 2700 4900 
L 3075 4650 
L 5000 4650 
L 5350 5000 
L 5850 4500 
L 3075 4500 
L 3075 2600 
L 5150 2600 
L 5500 2950 
L 6000 2450 
L 4275 2450 
Q 4425 1850 4675 1375 
Q 5300 1825 5550 2150 
L 5900 1725 
Q 5550 1725 4750 1250 
Q 5050 675 5437 375 
Q 5825 75 6125 25 
L 6125 -75 
Q 5725 -100 5550 -375 
Q 4400 700 4150 2450 
L 3750 2450 
L 3750 150 
z
M 3300 3625 
L 4925 3625 
L 5225 3925 
L 5675 3475 
L 4050 3475 
Q 3725 3475 3525 3400 
L 3300 3625 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-52a8" d="M 5950 3500 
L 5750 3325 
Q 5725 675 5587 137 
Q 5450 -400 4850 -575 
Q 4825 -300 4075 25 
L 4100 150 
Q 4625 25 4925 25 
Q 5075 25 5162 87 
Q 5250 150 5312 737 
Q 5375 1325 5425 3425 
L 4375 3425 
Q 4350 2475 4162 1725 
Q 3975 975 3487 400 
Q 3000 -175 2200 -625 
L 2125 -550 
Q 2925 0 3312 562 
Q 3700 1125 3862 1825 
Q 4025 2525 4050 3425 
L 3600 3425 
L 3300 3375 
L 3100 3575 
L 4050 3575 
Q 4050 4625 4025 5175 
L 4550 4925 
L 4375 4750 
L 4375 3575 
L 5375 3575 
L 5575 3800 
L 5950 3500 
z
M 475 4325 
L 2275 4325 
L 2575 4625 
L 3025 4175 
L 1200 4175 
Q 925 4175 700 4100 
L 475 4325 
z
M 275 3050 
L 2625 3050 
L 2925 3350 
L 3375 2900 
L 1700 2900 
L 2100 2600 
Q 1850 2500 1450 1875 
Q 1025 1250 675 875 
L 2650 1100 
Q 2400 1600 2125 2025 
L 2200 2075 
Q 3075 1275 3125 987 
Q 3175 700 3062 587 
Q 2950 475 2925 475 
Q 2850 475 2825 600 
Q 2775 775 2700 975 
Q 875 650 600 350 
L 325 850 
Q 600 925 1025 1687 
Q 1450 2450 1550 2900 
L 750 2900 
L 475 2850 
L 275 3050 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6570" d="M 1875 1525 
Q 1675 1200 1500 900 
Q 1825 825 2275 725 
Q 2500 975 2725 1525 
L 1875 1525 
z
M 850 4900 
Q 1325 4600 1425 4450 
Q 1525 4300 1525 4225 
Q 1525 4100 1437 3975 
Q 1350 3850 1300 3850 
Q 1225 3850 1150 4100 
Q 1050 4450 775 4850 
L 850 4900 
z
M 3000 4975 
L 3450 4675 
Q 3250 4625 3100 4425 
Q 2925 4225 2525 3825 
L 2450 3875 
Q 2925 4625 3000 4975 
z
M 1875 5250 
L 2425 5000 
L 2225 4825 
L 2225 3675 
L 2850 3675 
L 3125 3950 
L 3550 3525 
L 2225 3525 
L 2225 3300 
Q 2800 3175 2937 3062 
Q 3075 2950 3075 2775 
Q 3075 2700 3050 2587 
Q 3025 2475 2975 2475 
Q 2925 2475 2800 2650 
Q 2600 2925 2225 3175 
L 2225 2400 
L 1925 2200 
L 2275 2050 
Q 2150 2000 1950 1675 
L 2725 1675 
L 2900 1900 
L 3275 1575 
L 3050 1450 
Q 2800 875 2600 650 
Q 3000 550 3137 437 
Q 3275 325 3275 150 
Q 3275 -25 3200 -25 
Q 3125 -25 3025 75 
Q 2750 275 2400 425 
Q 1525 -275 325 -500 
L 300 -425 
Q 1450 -50 2100 550 
Q 1600 725 1125 800 
Q 1225 950 1525 1525 
L 1200 1525 
Q 875 1525 600 1450 
L 375 1675 
L 1575 1675 
Q 1675 1925 1775 2275 
L 1900 2225 
L 1900 3250 
Q 1225 2475 350 2050 
L 300 2125 
Q 1225 2775 1700 3525 
L 1250 3525 
Q 925 3525 650 3450 
L 425 3675 
L 1900 3675 
Q 1900 4575 1875 5250 
z
M 3925 3525 
Q 4050 2075 4525 1100 
Q 4925 1950 5025 3525 
L 3925 3525 
z
M 4050 5250 
L 4625 4925 
Q 4425 4850 4325 4625 
Q 4225 4400 3950 3675 
L 5375 3675 
L 5675 3975 
L 6125 3525 
L 5400 3525 
Q 5250 1675 4750 775 
Q 5325 75 6100 -100 
L 6100 -200 
Q 5625 -275 5600 -500 
Q 4950 -50 4525 525 
Q 3700 -275 2550 -650 
L 2500 -575 
Q 3650 -50 4325 800 
Q 3850 1975 3825 3375 
Q 3575 2725 3100 2150 
L 3025 2200 
Q 3625 3200 4050 5250 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-636e" d="M 3125 -650 
Q 3150 -100 3150 512 
Q 3150 1125 3125 1600 
L 3475 1400 
L 4175 1400 
L 4175 2400 
L 3000 2400 
Q 2825 625 1675 -525 
L 1600 -450 
Q 2150 275 2437 1187 
Q 2725 2100 2737 3312 
Q 2750 4525 2725 5050 
L 3100 4825 
L 5150 4825 
L 5350 5025 
L 5700 4700 
L 5500 4575 
Q 5500 3825 5525 3475 
L 5175 3325 
L 5175 3625 
L 3075 3625 
Q 3075 3150 3025 2550 
L 4175 2550 
Q 4175 3025 4150 3475 
L 4700 3225 
L 4500 3075 
L 4500 2550 
L 5325 2550 
L 5600 2825 
L 6025 2400 
L 4500 2400 
L 4500 1400 
L 5150 1400 
L 5350 1625 
L 5700 1300 
L 5500 1150 
Q 5500 150 5525 -400 
L 5175 -600 
L 5175 -75 
L 3475 -75 
L 3475 -500 
L 3125 -650 
z
M 3075 4675 
L 3075 3775 
L 5175 3775 
L 5175 4675 
L 3075 4675 
z
M 3475 1250 
L 3475 75 
L 5175 75 
L 5175 1250 
L 3475 1250 
z
M 1675 2450 
L 2500 2925 
L 2550 2825 
L 1675 2175 
L 1675 150 
Q 1650 -300 1125 -500 
Q 1175 -250 500 25 
L 500 125 
Q 1050 25 1187 37 
Q 1325 50 1325 225 
L 1325 2000 
Q 625 1500 600 1350 
L 225 1800 
Q 375 1825 1325 2300 
L 1325 3575 
L 750 3575 
L 450 3525 
L 250 3725 
L 1325 3725 
Q 1325 4750 1300 5175 
L 1875 4875 
L 1675 4700 
L 1675 3725 
L 1950 3725 
L 2275 4000 
L 2650 3575 
L 1675 3575 
L 1675 2450 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-9884" d="M 475 4825 
L 2225 4825 
L 2425 5075 
L 2825 4675 
Q 2600 4625 2387 4412 
Q 2175 4200 1800 3750 
Q 2050 3575 2037 3400 
Q 2025 3225 1937 3150 
Q 1850 3075 1825 3075 
Q 1725 3075 1650 3300 
Q 1525 3600 1050 4100 
L 1100 4150 
Q 1475 3975 1700 3825 
L 2225 4675 
L 1300 4675 
Q 975 4675 700 4600 
L 475 4825 
z
M 725 100 
Q 1175 50 1362 37 
Q 1550 25 1550 275 
L 1550 2875 
L 1125 2875 
Q 800 2875 525 2800 
L 300 3025 
L 2525 3025 
L 2725 3275 
L 3150 2875 
Q 2800 2825 2275 2075 
L 2200 2125 
L 2550 2875 
L 1875 2875 
L 1875 150 
Q 1875 -400 1350 -500 
Q 1325 -175 725 0 
L 725 100 
z
M 3250 775 
Q 3275 1425 3275 2287 
Q 3275 3150 3250 3900 
L 3600 3700 
L 4050 3700 
Q 4175 4400 4200 4675 
L 3650 4675 
Q 3325 4675 3050 4600 
L 2825 4825 
L 5325 4825 
L 5675 5150 
L 6125 4675 
L 4675 4675 
Q 4500 4425 4175 3700 
L 5300 3700 
L 5475 3975 
L 5875 3675 
L 5675 3475 
L 5675 1950 
Q 5675 1525 5700 1075 
L 5350 875 
L 5350 3550 
L 3600 3550 
L 3600 1000 
L 3250 775 
z
M 4850 2875 
L 4625 2675 
Q 4625 1475 4475 937 
Q 4325 400 3900 50 
Q 3475 -300 2425 -625 
L 2400 -550 
Q 3500 -50 3837 400 
Q 4175 850 4237 1587 
Q 4300 2325 4275 3175 
L 4850 2875 
z
M 4650 950 
Q 5725 375 5875 212 
Q 6025 50 6025 -125 
Q 6025 -225 5962 -387 
Q 5900 -550 5850 -550 
Q 5800 -550 5750 -450 
Q 5625 -250 5437 0 
Q 5250 250 4600 875 
L 4650 950 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6d4b" d="M 1975 1000 
Q 2000 1475 2000 2925 
Q 2000 4375 1975 5000 
L 2350 4775 
L 3575 4775 
L 3775 4975 
L 4100 4650 
L 3900 4500 
L 3900 2350 
Q 3900 1850 3925 1350 
L 3600 1200 
L 3600 4625 
L 2325 4625 
L 2325 1150 
L 1975 1000 
z
M 2775 3975 
L 3300 3700 
L 3125 3525 
Q 3150 1825 2875 937 
Q 2600 50 1375 -600 
L 1325 -500 
Q 2200 100 2475 725 
Q 2750 1350 2775 2187 
Q 2800 3025 2775 3975 
z
M 3100 950 
Q 3825 450 3937 262 
Q 4050 75 4050 -25 
Q 4050 -150 3975 -262 
Q 3900 -375 3850 -375 
Q 3750 -375 3650 -75 
Q 3500 375 3050 875 
L 3100 950 
z
M 4475 825 
Q 4500 1200 4500 1700 
L 4500 3400 
Q 4500 3950 4475 4300 
L 5025 4025 
L 4825 3825 
L 4825 1700 
Q 4825 1325 4850 950 
L 4475 825 
z
M 5375 150 
L 5375 4050 
Q 5375 4750 5350 5125 
L 5875 4875 
L 5700 4700 
L 5700 75 
Q 5700 -200 5600 -350 
Q 5500 -500 5200 -600 
Q 5025 -225 4450 -50 
L 4450 50 
Q 5125 -50 5250 -37 
Q 5375 -25 5375 150 
z
M 1850 3500 
Q 1375 1525 1300 1225 
Q 1200 900 1212 450 
Q 1225 0 1250 -175 
Q 1250 -275 1125 -275 
Q 1025 -275 875 -225 
Q 725 -175 725 0 
Q 725 125 800 375 
Q 875 625 875 775 
Q 875 950 762 1050 
Q 650 1150 275 1250 
L 275 1350 
Q 725 1300 837 1312 
Q 950 1325 1087 1537 
Q 1225 1750 1750 3525 
L 1850 3500 
z
M 375 3775 
Q 1075 3350 1112 3100 
Q 1150 2850 1050 2775 
Q 950 2700 900 2700 
Q 800 2700 725 2975 
Q 600 3350 325 3700 
L 375 3775 
z
M 800 5100 
Q 1475 4725 1550 4512 
Q 1625 4300 1525 4187 
Q 1425 4075 1350 4075 
Q 1275 4075 1200 4300 
Q 1075 4650 750 5025 
L 800 5100 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-7ed3" d="M 4050 3900 
Q 4050 4925 4025 5250 
L 4625 4925 
L 4375 4750 
L 4375 3900 
L 5300 3900 
L 5600 4200 
L 6025 3750 
L 4375 3750 
L 4375 2625 
L 5050 2625 
L 5350 2925 
L 5775 2475 
L 3650 2475 
Q 3325 2475 3050 2400 
L 2825 2625 
L 4050 2625 
L 4050 3750 
L 3425 3750 
Q 3100 3750 2825 3675 
L 2600 3900 
L 4050 3900 
z
M 3025 -600 
Q 3050 -50 3050 675 
Q 3050 1400 3025 1925 
L 3425 1700 
L 5125 1700 
L 5350 1925 
L 5700 1575 
L 5475 1425 
L 5475 400 
Q 5475 -150 5500 -375 
L 5150 -525 
L 5150 -25 
L 3375 -25 
L 3375 -425 
L 3025 -600 
z
M 3375 1550 
L 3375 125 
L 5150 125 
L 5150 1550 
L 3375 1550 
z
M 975 1550 
L 2700 1825 
L 2700 1725 
Q 2400 1650 1750 1462 
Q 1100 1275 800 1050 
L 525 1500 
Q 725 1575 900 1750 
Q 1075 1925 1725 2825 
Q 1425 2775 1150 2712 
Q 875 2650 625 2500 
L 400 2950 
Q 650 2950 987 3662 
Q 1325 4375 1525 5125 
L 2050 4850 
L 1825 4725 
Q 1300 3725 800 2950 
L 1800 2975 
Q 2100 3450 2275 3850 
L 2725 3525 
L 2500 3400 
Q 1800 2475 975 1550 
z
M 650 -250 
L 400 225 
Q 750 275 1237 387 
Q 1725 500 2750 800 
L 2775 700 
Q 925 -25 650 -250 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-679c" d="M 1725 4725 
L 1725 3875 
L 3025 3875 
L 3025 4725 
L 1725 4725 
z
M 3400 4725 
L 3400 3875 
L 4725 3875 
L 4725 4725 
L 3400 4725 
z
M 1725 3725 
L 1725 2825 
L 3025 2825 
L 3025 3725 
L 1725 3725 
z
M 3400 3725 
L 3400 2825 
L 4725 2825 
L 4725 3725 
L 3400 3725 
z
M 1350 2300 
Q 1375 3000 1375 3700 
Q 1375 4400 1350 5075 
L 1725 4875 
L 4700 4875 
L 4925 5100 
L 5300 4750 
L 5075 4600 
Q 5075 2850 5100 2500 
L 4725 2325 
L 4725 2675 
L 3400 2675 
L 3400 1950 
L 5100 1950 
L 5475 2325 
L 6000 1800 
L 3450 1800 
Q 4500 450 6025 100 
L 6025 0 
Q 5550 -25 5450 -300 
Q 3950 525 3400 1700 
Q 3400 -50 3425 -425 
L 3000 -625 
Q 3025 350 3025 1650 
Q 2100 325 400 -375 
L 375 -275 
Q 1850 550 2700 1800 
L 1300 1800 
Q 975 1800 700 1725 
L 475 1950 
L 3025 1950 
L 3025 2675 
L 1725 2675 
L 1725 2450 
L 1350 2300 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimSun-632f"/>
     <use xlink:href="#SimSun-52a8" x="100"/>
     <use xlink:href="#SimSun-6570" x="200"/>
     <use xlink:href="#SimSun-636e" x="300"/>
     <use xlink:href="#SimSun-9884" x="400"/>
     <use xlink:href="#SimSun-6d4b" x="500"/>
     <use xlink:href="#SimSun-7ed3" x="600"/>
     <use xlink:href="#SimSun-679c" x="700"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="line2d_31">
     <path d="M 904.010391 37.333594 
L 914.510391 37.333594 
L 925.010391 37.333594 
" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_18">
     <!-- 真实振动位移 -->
     <g transform="translate(933.410391 41.008594)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-771f" d="M 2050 3450 
L 2050 2900 
L 4300 2900 
L 4300 3450 
L 2050 3450 
z
M 2050 2750 
L 2050 2225 
L 4300 2225 
L 4300 2750 
L 2050 2750 
z
M 2050 2075 
L 2050 1550 
L 4300 1550 
L 4300 2075 
L 2050 2075 
z
M 2050 1400 
L 2050 875 
L 4300 875 
L 4300 1400 
L 2050 1400 
z
M 2875 3600 
L 2925 4325 
L 1525 4325 
Q 1200 4325 925 4250 
L 700 4475 
L 2950 4475 
Q 2975 4825 2975 5275 
L 3550 5025 
L 3375 4925 
L 3300 4475 
L 5000 4475 
L 5325 4825 
L 5775 4325 
L 3275 4325 
L 3200 3600 
L 4275 3600 
L 4450 3875 
L 4850 3575 
L 4650 3375 
L 4650 875 
L 5300 875 
L 5675 1225 
L 6125 725 
L 1150 725 
Q 825 725 550 650 
L 325 875 
L 1700 875 
Q 1700 3200 1675 3775 
L 2050 3600 
L 2875 3600 
z
M 2425 725 
L 2825 350 
L 2575 300 
Q 1500 -425 800 -650 
L 775 -575 
Q 1950 125 2425 725 
z
M 3800 700 
Q 4575 375 4962 200 
Q 5350 25 5400 -112 
Q 5450 -250 5450 -325 
Q 5450 -400 5400 -525 
Q 5350 -650 5325 -650 
Q 5250 -650 5100 -450 
Q 4875 -150 3750 625 
L 3800 700 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5b9e" d="M 1950 3625 
Q 2575 3400 2700 3250 
Q 2825 3100 2825 2975 
Q 2825 2850 2750 2700 
Q 2675 2550 2625 2550 
Q 2550 2550 2450 2800 
Q 2275 3175 1900 3550 
L 1950 3625 
z
M 3575 1325 
Q 3500 1100 3425 925 
Q 4650 550 5087 325 
Q 5525 100 5512 -225 
Q 5500 -550 5400 -550 
Q 5300 -550 5075 -300 
Q 4625 175 3375 800 
Q 3025 175 2300 -150 
Q 1575 -475 425 -625 
L 425 -525 
Q 1475 -325 2200 100 
Q 2925 525 3200 1325 
L 1225 1325 
Q 900 1325 625 1250 
L 400 1475 
L 3225 1475 
Q 3325 1950 3375 2575 
Q 3425 3200 3425 3700 
L 4050 3400 
L 3850 3225 
Q 3750 2300 3625 1475 
L 5175 1475 
L 5550 1850 
L 6075 1325 
L 3575 1325 
z
M 1275 2700 
Q 1975 2425 2100 2262 
Q 2225 2100 2225 1975 
Q 2225 1850 2150 1700 
Q 2075 1550 2025 1550 
Q 1950 1550 1850 1800 
Q 1675 2175 1225 2625 
L 1275 2700 
z
M 2750 5225 
L 2825 5275 
Q 3225 5100 3425 4925 
Q 3625 4750 3625 4600 
Q 3625 4450 3512 4300 
Q 3400 4150 3325 4150 
Q 3250 4150 3200 4400 
Q 3100 4775 2750 5225 
z
M 4975 3225 
L 5350 3925 
L 1175 3925 
Q 1225 3525 1125 3300 
Q 1025 3075 787 3100 
Q 550 3125 550 3200 
Q 550 3300 700 3475 
Q 975 3825 1050 4375 
L 1150 4375 
L 1175 4075 
L 5300 4075 
L 5575 4350 
L 6050 3800 
Q 5625 3825 5050 3175 
L 4975 3225 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-771f"/>
      <use xlink:href="#SimSun-5b9e" x="100"/>
      <use xlink:href="#SimSun-632f" x="200"/>
      <use xlink:href="#SimSun-52a8" x="300"/>
      <use xlink:href="#SimSun-4f4d" x="400"/>
      <use xlink:href="#SimSun-79fb" x="500"/>
     </g>
    </g>
    <g id="line2d_32">
     <path d="M 904.010391 52.222266 
L 914.510391 52.222266 
L 925.010391 52.222266 
" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
    </g>
    <g id="text_19">
     <!-- LSTM+卡尔曼滤波预测位移 -->
     <g transform="translate(933.410391 55.897266)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-4c" d="M 1000 450 
Q 1000 350 1062 300 
Q 1125 250 1225 250 
L 1900 250 
Q 2250 250 2500 462 
Q 2750 675 2875 1125 
L 3000 1075 
L 2775 100 
L 200 100 
L 200 250 
L 375 250 
Q 500 250 550 300 
Q 600 350 600 450 
L 600 4025 
Q 600 4125 550 4175 
Q 500 4225 375 4225 
L 225 4225 
L 225 4375 
L 1400 4375 
L 1400 4225 
L 1225 4225 
Q 1125 4225 1062 4175 
Q 1000 4125 1000 4025 
L 1000 450 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-53" d="M 1500 2150 
Q 825 2450 575 2675 
Q 325 2900 325 3375 
Q 325 3750 637 4087 
Q 950 4425 1525 4425 
Q 1775 4425 2000 4350 
Q 2225 4250 2325 4250 
Q 2400 4250 2462 4287 
Q 2525 4325 2575 4425 
L 2675 3400 
L 2550 3350 
Q 2325 3875 2087 4062 
Q 1850 4250 1475 4250 
Q 1000 4250 825 3987 
Q 650 3725 650 3425 
Q 650 3150 812 2987 
Q 975 2825 1700 2500 
Q 2300 2250 2562 1937 
Q 2825 1625 2825 1200 
Q 2825 750 2512 400 
Q 2200 50 1600 50 
Q 1350 50 1150 150 
Q 925 225 800 225 
Q 725 225 650 175 
Q 575 125 475 50 
L 275 1275 
L 425 1325 
Q 600 700 912 462 
Q 1225 225 1625 225 
Q 2050 225 2262 475 
Q 2475 725 2475 1200 
Q 2475 1450 2237 1687 
Q 2000 1925 1500 2150 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-54" d="M 2225 100 
L 925 100 
L 925 250 
L 1150 250 
Q 1275 250 1325 300 
Q 1375 350 1375 450 
L 1375 3950 
Q 1375 4150 1350 4175 
Q 1325 4200 1150 4200 
Q 800 4200 662 4075 
Q 525 3950 325 3400 
L 175 3425 
L 400 4375 
L 2775 4375 
L 2975 3425 
L 2850 3400 
Q 2650 3925 2537 4062 
Q 2425 4200 2075 4200 
Q 1825 4200 1800 4175 
Q 1775 4150 1775 3925 
L 1775 450 
Q 1775 350 1837 300 
Q 1900 250 2000 250 
L 2225 250 
L 2225 100 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-4d" d="M 2800 450 
Q 2800 350 2862 300 
Q 2925 250 3025 250 
L 3125 250 
L 3125 100 
L 2050 100 
L 2050 250 
L 2175 250 
Q 2300 250 2350 300 
Q 2400 350 2400 450 
L 2400 3725 
L 2350 3725 
L 1500 100 
L 1350 100 
L 575 3650 
L 525 3650 
L 525 450 
Q 525 350 587 300 
Q 650 250 750 250 
L 825 250 
L 825 100 
L 75 100 
L 75 250 
L 150 250 
Q 275 250 325 300 
Q 375 350 375 450 
L 375 4025 
Q 375 4125 325 4175 
Q 275 4225 150 4225 
L 50 4225 
L 50 4375 
L 775 4375 
L 1500 1025 
L 1550 1025 
L 2325 4375 
L 3125 4375 
L 3125 4225 
L 3025 4225 
Q 2925 4225 2862 4175 
Q 2800 4125 2800 4025 
L 2800 450 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-2b" d="M 1525 2450 
L 1525 3875 
L 1750 3875 
L 1750 2450 
L 3000 2450 
L 3000 2225 
L 1750 2225 
L 1750 800 
L 1525 800 
L 1525 2225 
L 275 2225 
L 275 2450 
L 1525 2450 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5361" d="M 2675 -600 
Q 2700 75 2700 475 
L 2700 2675 
L 1150 2675 
Q 825 2675 550 2600 
L 325 2825 
L 2700 2825 
L 2700 4400 
Q 2700 4800 2675 5250 
L 3275 4950 
L 3050 4775 
L 3050 4100 
L 4650 4100 
L 4975 4425 
L 5450 3950 
L 3050 3950 
L 3050 2825 
L 5175 2825 
L 5550 3200 
L 6075 2675 
L 2800 2675 
L 3275 2400 
L 3050 2250 
L 3050 1975 
Q 4875 1550 5100 1300 
Q 5325 1050 5287 862 
Q 5250 675 5175 675 
Q 5075 675 4850 875 
Q 4475 1225 3050 1850 
L 3050 350 
Q 3050 -100 3075 -425 
L 2675 -600 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-5c14" d="M 4650 3050 
L 5100 3900 
L 1925 3900 
Q 1375 3100 825 2575 
L 750 2650 
Q 1225 3250 1562 3925 
Q 1900 4600 2025 5225 
L 2550 4975 
Q 2425 4850 2300 4650 
Q 2175 4450 1975 4050 
L 5100 4050 
L 5375 4350 
L 5850 3875 
Q 5450 3850 4725 3000 
L 4650 3050 
z
M 3150 325 
L 3150 2300 
Q 3150 3025 3125 3400 
L 3700 3100 
L 3500 2950 
L 3500 125 
Q 3450 -425 2925 -625 
Q 2900 -275 2125 -25 
L 2125 100 
Q 2825 0 2975 12 
Q 3125 25 3150 325 
z
M 2000 2550 
L 2550 2175 
Q 2350 2075 2225 1875 
Q 2100 1675 1625 1062 
Q 1150 450 425 -100 
L 375 -25 
Q 975 575 1400 1250 
Q 1825 1925 2000 2550 
z
M 4100 2425 
Q 4800 1875 5212 1437 
Q 5625 1000 5700 737 
Q 5775 475 5675 337 
Q 5575 200 5500 200 
Q 5400 200 5300 475 
Q 5125 900 4812 1375 
Q 4500 1850 4025 2350 
L 4100 2425 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-66fc" d="M 2125 1275 
Q 2600 800 3175 475 
Q 3725 800 4175 1275 
L 2125 1275 
z
M 4850 4675 
Q 4850 3700 4875 3325 
L 4525 3175 
L 4525 3400 
L 1900 3400 
L 1900 3275 
L 1550 3150 
Q 1575 3600 1575 4075 
Q 1575 4550 1550 5125 
L 1900 4925 
L 4475 4925 
L 4700 5150 
L 5075 4850 
L 4850 4675 
z
M 1900 4775 
L 1900 4250 
L 4525 4250 
L 4525 4775 
L 1900 4775 
z
M 1900 4100 
L 1900 3550 
L 4525 3550 
L 4525 4100 
L 1900 4100 
z
M 900 1575 
Q 925 2000 925 2400 
Q 925 2800 900 3150 
L 1275 2975 
L 5050 2975 
L 5275 3200 
L 5650 2900 
L 5425 2750 
Q 5425 2050 5450 1775 
L 5100 1625 
L 5100 1875 
L 1250 1875 
L 1250 1725 
L 900 1575 
z
M 1250 2825 
L 1250 2025 
L 2275 2025 
L 2275 2825 
L 1250 2825 
z
M 2625 2825 
L 2625 2025 
L 3700 2025 
L 3700 2825 
L 2625 2825 
z
M 4025 2825 
L 4025 2025 
L 5100 2025 
L 5100 2825 
L 4025 2825 
z
M 4525 1100 
Q 3875 575 3450 325 
Q 3900 100 4487 12 
Q 5075 -75 5425 -75 
Q 5750 -75 6075 -25 
L 6075 -125 
Q 5600 -250 5575 -500 
Q 4850 -475 4237 -312 
Q 3625 -150 3175 150 
Q 2475 -200 1737 -362 
Q 1000 -525 400 -600 
L 350 -500 
Q 1000 -400 1687 -187 
Q 2375 25 2925 325 
Q 2425 725 2000 1275 
L 1775 1275 
L 1475 1225 
L 1275 1425 
L 4200 1425 
L 4450 1625 
L 4825 1200 
L 4525 1100 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6ee4" d="M 3025 1175 
L 3125 1150 
Q 3125 575 3050 337 
Q 2975 100 2825 50 
Q 2675 0 2562 37 
Q 2450 75 2450 125 
Q 2450 225 2600 350 
Q 2825 550 3025 1175 
z
M 3950 1775 
Q 4475 1550 4587 1412 
Q 4700 1275 4700 1175 
Q 4700 1050 4637 925 
Q 4575 800 4525 800 
Q 4450 800 4375 1025 
Q 4250 1350 3900 1700 
L 3950 1775 
z
M 5275 1175 
Q 5725 925 5875 762 
Q 6025 600 6025 450 
Q 6025 325 5950 200 
Q 5875 75 5850 75 
Q 5775 75 5725 300 
Q 5625 650 5225 1125 
L 5275 1175 
z
M 3925 -350 
Q 3475 -375 3450 75 
L 3450 850 
Q 3450 1175 3425 1475 
L 3975 1200 
L 3775 1050 
L 3775 200 
Q 3750 -75 4100 -75 
L 4825 -75 
Q 5025 -50 5050 212 
Q 5075 475 5100 800 
L 5200 800 
Q 5200 475 5237 250 
Q 5275 25 5500 -50 
Q 5325 -350 4875 -350 
L 3925 -350 
z
M 3725 3750 
Q 3725 4875 3700 5250 
L 4275 5025 
L 4050 4825 
L 4050 4450 
L 4950 4450 
L 5275 4775 
L 5700 4300 
L 4050 4300 
L 4050 3750 
L 5450 3750 
L 5650 4000 
L 6050 3575 
Q 5750 3600 5325 3100 
L 5250 3150 
L 5500 3600 
L 2700 3600 
Q 2725 2000 2525 1112 
Q 2325 225 1475 -575 
L 1400 -525 
Q 1875 25 2087 662 
Q 2300 1300 2337 1962 
Q 2375 2625 2375 3075 
Q 2375 3525 2350 3975 
L 2700 3750 
L 3725 3750 
z
M 5125 2200 
Q 5300 2250 5325 2525 
Q 5350 2775 5375 2975 
L 5475 2975 
Q 5475 2700 5512 2487 
Q 5550 2275 5750 2200 
Q 5575 1925 5150 1950 
L 4225 1950 
Q 3650 1925 3700 2325 
L 3700 2725 
L 3075 2675 
L 2950 2600 
L 2750 2800 
L 3700 2875 
Q 3700 3275 3675 3600 
L 4225 3375 
L 4025 3225 
L 4025 2925 
L 4575 3000 
L 4825 3275 
L 5225 2900 
L 4025 2775 
L 4025 2375 
Q 4025 2175 4350 2200 
L 5125 2200 
z
M 2200 4025 
Q 1400 1400 1337 1062 
Q 1275 725 1275 275 
Q 1275 -175 1275 -325 
Q 1275 -475 1175 -475 
Q 1075 -475 925 -412 
Q 775 -350 775 -175 
Q 775 -50 850 200 
Q 925 450 925 600 
Q 925 775 812 875 
Q 700 975 325 1075 
L 325 1150 
Q 775 1125 875 1150 
Q 975 1175 1112 1375 
Q 1250 1575 2100 4050 
L 2200 4025 
z
M 350 3475 
Q 1125 3100 1162 2887 
Q 1200 2675 1100 2550 
Q 1000 2425 950 2425 
Q 850 2425 800 2625 
Q 675 2950 300 3400 
L 350 3475 
z
M 925 4900 
Q 1650 4575 1725 4362 
Q 1800 4150 1687 3987 
Q 1575 3825 1525 3825 
Q 1450 3825 1375 4050 
Q 1250 4375 875 4850 
L 925 4900 
z
" transform="scale(0.015625)"/>
       <path id="SimSun-6ce2" d="M 4975 3175 
L 5300 3800 
L 4200 3800 
L 4200 2525 
L 5000 2525 
L 5225 2750 
L 5575 2400 
L 5325 2225 
Q 4975 1450 4400 750 
Q 5200 25 6075 -150 
L 6075 -225 
Q 5700 -250 5525 -525 
Q 4750 -75 4200 525 
Q 3325 -325 1950 -625 
L 1925 -525 
Q 3175 -125 4000 750 
Q 3500 1400 3225 2375 
L 3075 2375 
L 2925 2325 
L 2775 2450 
Q 2775 1550 2450 787 
Q 2125 25 1350 -600 
L 1275 -525 
Q 1950 200 2200 925 
Q 2450 1650 2450 2550 
Q 2450 3450 2425 4175 
L 2800 3950 
L 3875 3950 
Q 3875 4950 3850 5250 
L 4375 5050 
L 4200 4850 
L 4200 3950 
L 5250 3950 
L 5525 4225 
L 5950 3800 
Q 5725 3725 5562 3625 
Q 5400 3525 5050 3125 
L 4975 3175 
z
M 2775 3800 
L 2775 2525 
L 3875 2525 
L 3875 3800 
L 2775 3800 
z
M 4200 975 
Q 4675 1550 5025 2375 
L 3350 2375 
Q 3725 1450 4200 975 
z
M 2350 4100 
Q 1375 1450 1287 1137 
Q 1200 825 1212 375 
Q 1225 -75 1250 -250 
Q 1250 -350 1125 -350 
Q 1025 -350 875 -300 
Q 725 -250 725 -75 
Q 725 50 800 300 
Q 875 550 875 700 
Q 875 875 762 975 
Q 650 1075 275 1175 
L 275 1275 
Q 725 1225 837 1237 
Q 950 1250 1087 1462 
Q 1225 1675 2250 4125 
L 2350 4100 
z
M 325 3650 
Q 1200 3200 1200 2912 
Q 1200 2625 1062 2525 
Q 925 2425 825 2775 
Q 700 3100 275 3575 
L 325 3650 
z
M 875 5075 
Q 1575 4725 1637 4550 
Q 1700 4375 1700 4300 
Q 1700 4175 1612 4075 
Q 1525 3975 1475 3975 
Q 1400 3975 1325 4225 
Q 1200 4550 825 5000 
L 875 5075 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-5361" x="250"/>
      <use xlink:href="#SimSun-5c14" x="350"/>
      <use xlink:href="#SimSun-66fc" x="450"/>
      <use xlink:href="#SimSun-6ee4" x="550"/>
      <use xlink:href="#SimSun-6ce2" x="650"/>
      <use xlink:href="#SimSun-9884" x="750"/>
      <use xlink:href="#SimSun-6d4b" x="850"/>
      <use xlink:href="#SimSun-4f4d" x="950"/>
      <use xlink:href="#SimSun-79fb" x="1050"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 48.756641 673.440156 
L 1063.610391 673.440156 
L 1063.610391 377.487656 
L 48.756641 377.487656 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_33">
      <path d="M 206.077745 673.440156 
L 206.077745 377.487656 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#ma73ec2b09a" x="206.077745" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 850 -->
      <g transform="translate(198.202745 687.946016)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-38" d="M 625 1125 
Q 625 725 887 475 
Q 1150 225 1550 225 
Q 2025 225 2237 475 
Q 2450 725 2450 1100 
Q 2450 1425 2150 1725 
Q 1850 2025 1250 2275 
Q 950 2075 787 1775 
Q 625 1475 625 1125 
z
M 2825 1175 
Q 2825 700 2475 375 
Q 2125 50 1550 50 
Q 1025 50 650 375 
Q 275 700 275 1125 
Q 275 1550 487 1850 
Q 700 2150 1100 2375 
Q 750 2550 562 2812 
Q 375 3075 375 3400 
Q 375 3825 700 4125 
Q 1025 4425 1600 4425 
Q 2125 4425 2450 4125 
Q 2775 3825 2775 3400 
Q 2775 3100 2587 2837 
Q 2400 2575 2000 2375 
Q 2425 2125 2625 1825 
Q 2825 1525 2825 1175 
z
M 2425 3375 
Q 2425 3725 2225 3987 
Q 2025 4250 1600 4250 
Q 1125 4250 925 4000 
Q 725 3750 725 3475 
Q 725 3200 1012 2937 
Q 1300 2675 1825 2475 
Q 2125 2625 2275 2850 
Q 2425 3075 2425 3375 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-38"/>
       <use xlink:href="#SimSun-35" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_35">
      <path d="M 430.235101 673.440156 
L 430.235101 377.487656 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#ma73ec2b09a" x="430.235101" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 900 -->
      <g transform="translate(422.360101 687.946016)scale(0.105 -0.105)">
       <defs>
        <path id="SimSun-39" d="M 700 3050 
Q 700 2500 912 2212 
Q 1125 1925 1450 1925 
Q 1825 1925 2100 2275 
Q 2375 2625 2375 2925 
Q 2375 3525 2175 3887 
Q 1975 4250 1525 4250 
Q 1100 4250 900 3912 
Q 700 3575 700 3050 
z
M 1375 1700 
Q 875 1700 575 2012 
Q 275 2325 275 3025 
Q 275 3625 600 4025 
Q 925 4425 1525 4425 
Q 2150 4425 2500 3925 
Q 2850 3425 2850 2475 
Q 2850 1425 2437 737 
Q 2025 50 1300 50 
Q 975 50 737 200 
Q 500 350 500 600 
Q 500 700 562 800 
Q 625 900 775 900 
Q 925 900 975 787 
Q 1025 675 1025 525 
Q 1025 425 1050 362 
Q 1075 300 1100 275 
Q 1125 250 1187 237 
Q 1250 225 1325 225 
Q 1775 225 2050 725 
Q 2325 1225 2375 2400 
Q 2250 2100 2000 1900 
Q 1750 1700 1375 1700 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimSun-39"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_37">
      <path d="M 654.392457 673.440156 
L 654.392457 377.487656 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#ma73ec2b09a" x="654.392457" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 950 -->
      <g transform="translate(646.517457 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-39"/>
       <use xlink:href="#SimSun-35" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_39">
      <path d="M 878.549813 673.440156 
L 878.549813 377.487656 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#ma73ec2b09a" x="878.549813" y="673.440156" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 1000 -->
      <g transform="translate(868.049813 687.946016)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-31"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
       <use xlink:href="#SimSun-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="text_24">
     <!-- 时间 (ms) -->
     <g transform="translate(532.558516 701.461641)scale(0.105 -0.105)">
      <defs>
       <path id="SimSun-73" d="M 625 2175 
Q 625 2550 912 2750 
Q 1200 2950 1600 2950 
Q 1825 2950 2025 2900 
Q 2225 2825 2300 2825 
Q 2375 2825 2425 2850 
Q 2475 2875 2550 2950 
L 2650 2125 
L 2500 2100 
Q 2400 2425 2162 2612 
Q 1925 2800 1625 2800 
Q 1300 2800 1112 2675 
Q 925 2550 925 2325 
Q 925 2125 1012 2012 
Q 1100 1900 1325 1850 
Q 1500 1775 1775 1700 
Q 2025 1600 2250 1500 
Q 2450 1400 2587 1237 
Q 2725 1075 2725 875 
Q 2725 500 2462 287 
Q 2200 75 1700 75 
Q 1375 75 1225 150 
Q 1050 200 950 200 
Q 875 200 787 162 
Q 700 125 600 75 
L 525 1000 
L 675 1025 
Q 750 625 1000 425 
Q 1250 225 1700 225 
Q 2050 225 2237 350 
Q 2425 475 2425 725 
Q 2425 900 2325 1012 
Q 2225 1125 2125 1175 
Q 1875 1275 1575 1400 
Q 1275 1500 1050 1600 
Q 825 1700 725 1850 
Q 625 2000 625 2175 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimSun-65f6"/>
      <use xlink:href="#SimSun-95f4" x="100"/>
      <use xlink:href="#SimSun-20" x="200"/>
      <use xlink:href="#SimSun-28" x="250"/>
      <use xlink:href="#SimSun-6d" x="300"/>
      <use xlink:href="#SimSun-73" x="350"/>
      <use xlink:href="#SimSun-29" x="400"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_6">
     <g id="line2d_41">
      <path d="M 48.756641 659.85044 
L 1063.610391 659.85044 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="659.85044" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- -0.4 -->
      <g transform="translate(20.756641 663.60337)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-34" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_43">
      <path d="M 48.756641 600.203788 
L 1063.610391 600.203788 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="600.203788" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- -0.2 -->
      <g transform="translate(20.756641 603.956718)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-2d"/>
       <use xlink:href="#SimSun-30" x="50"/>
       <use xlink:href="#SimSun-2e" x="100"/>
       <use xlink:href="#SimSun-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_45">
      <path d="M 48.756641 540.557137 
L 1063.610391 540.557137 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="540.557137" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 0.0 -->
      <g transform="translate(26.006641 544.310067)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_47">
      <path d="M 48.756641 480.910485 
L 1063.610391 480.910485 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="480.910485" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 0.2 -->
      <g transform="translate(26.006641 484.663415)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_49">
      <path d="M 48.756641 421.263834 
L 1063.610391 421.263834 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mdadd275ed6" x="48.756641" y="421.263834" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 0.4 -->
      <g transform="translate(26.006641 425.016764)scale(0.105 -0.105)">
       <use xlink:href="#SimSun-30"/>
       <use xlink:href="#SimSun-2e" x="50"/>
       <use xlink:href="#SimSun-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_30">
     <!-- 位移 (μm) -->
     <g transform="translate(15.403125 551.713906)rotate(-90)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-4f4d"/>
      <use xlink:href="#SimSun-79fb" x="100"/>
      <use xlink:href="#SimSun-20" x="200"/>
      <use xlink:href="#SimSun-28" x="250"/>
      <use xlink:href="#SimSun-3bc" x="300"/>
      <use xlink:href="#SimSun-6d" x="400"/>
      <use xlink:href="#SimSun-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="line2d_51">
    <path d="M 94.886357 553.501166 
L 97.221329 546.167837 
L 99.509602 540.861294 
L 101.844574 535.766527 
L 104.132847 529.101075 
L 106.46782 519.117585 
L 108.756093 506.120587 
L 113.379338 476.602852 
L 115.714311 464.396404 
L 118.002584 455.751043 
L 120.337556 449.807372 
L 124.960802 441.106904 
L 127.249075 436.335696 
L 131.87232 426.208077 
L 134.207293 421.743887 
L 138.830538 413.577578 
L 143.453784 404.831968 
L 145.742057 401.523232 
L 148.077029 400.111727 
L 150.365302 401.090831 
L 152.700275 404.183714 
L 154.988548 408.431149 
L 159.611793 417.620154 
L 164.235039 426.755495 
L 171.193257 441.814787 
L 175.816502 451.091232 
L 178.151474 456.713061 
L 180.439747 464.010038 
L 182.77472 473.792559 
L 185.062993 485.41621 
L 189.686238 510.499172 
L 192.021211 521.125442 
L 194.309484 529.077625 
L 196.644456 534.937759 
L 198.932729 539.580369 
L 201.267702 544.781685 
L 203.555975 551.7036 
L 205.890947 561.288267 
L 208.17922 572.815134 
L 212.802466 597.584864 
L 215.137438 608.124317 
L 217.425711 616.250588 
L 219.760684 622.480863 
L 222.048957 627.162991 
L 224.383929 631.133153 
L 226.672202 634.631656 
L 229.007175 637.937697 
L 231.295448 640.902176 
L 233.63042 643.601174 
L 235.918693 645.887251 
L 238.253666 647.778189 
L 240.541939 649.077533 
L 242.876911 649.736476 
L 245.165184 649.754257 
L 247.500156 649.312235 
L 249.788429 648.640132 
L 252.123402 647.775119 
L 254.411675 646.571456 
L 256.746647 644.660589 
L 259.03492 641.943188 
L 261.369893 638.482099 
L 265.993138 631.367777 
L 268.328111 627.950567 
L 270.616384 623.949104 
L 272.951356 618.087857 
L 275.239629 609.784147 
L 277.574602 598.710507 
L 282.197847 573.588788 
L 284.48612 562.557331 
L 286.821093 553.508123 
L 289.109366 546.720522 
L 291.444338 541.24903 
L 296.067584 531.536605 
L 298.355857 525.975882 
L 300.690829 519.062724 
L 302.979102 510.707116 
L 305.314075 500.586616 
L 309.93732 478.916067 
L 312.225593 469.873581 
L 314.560566 463.077869 
L 316.848839 458.736901 
L 319.183811 455.805019 
L 321.472084 453.3302 
L 328.430302 445.19282 
L 330.718575 443.801747 
L 333.053547 443.553979 
L 335.34182 444.068196 
L 339.965066 445.567742 
L 342.300038 446.558371 
L 344.588311 448.181216 
L 346.923284 450.716598 
L 349.211557 453.844194 
L 353.834802 460.512235 
L 356.169775 464.1897 
L 358.504747 469.060049 
L 360.79302 475.775526 
L 363.127993 484.725354 
L 367.751238 504.812516 
L 370.039511 513.225882 
L 372.374484 519.769429 
L 374.662757 524.553009 
L 379.286002 532.953997 
L 381.620975 537.763719 
L 386.24422 547.640667 
L 393.155739 561.343896 
L 395.490711 567.019358 
L 402.402229 586.373333 
L 404.737202 591.825794 
L 407.025475 596.026168 
L 409.360447 599.248693 
L 411.64872 601.62641 
L 413.983693 603.499807 
L 416.271966 604.911839 
L 418.606938 605.945596 
L 420.895211 606.580845 
L 423.230184 606.93306 
L 427.853429 607.373757 
L 432.476675 608.129734 
L 434.764948 608.516029 
L 437.09992 608.749522 
L 439.388193 608.740723 
L 441.723166 608.483726 
L 444.011439 608.024744 
L 446.346411 607.347675 
L 448.634684 606.381766 
L 450.969657 604.875148 
L 453.304629 602.587592 
L 455.592902 599.422828 
L 457.927875 595.272563 
L 460.216148 590.5291 
L 464.839393 580.353508 
L 467.174365 575.551251 
L 469.462638 571.274123 
L 471.797611 567.374027 
L 474.085884 563.999125 
L 476.420856 561.00006 
L 478.709129 558.462455 
L 483.332375 554.10584 
L 487.95562 549.746666 
L 497.202111 540.415 
L 501.825357 535.75637 
L 511.071848 525.862341 
L 520.318339 516.988808 
L 524.941584 512.112688 
L 527.276557 509.48545 
L 529.56483 506.558166 
L 531.899802 502.931786 
L 534.188075 498.588428 
L 536.523048 493.439803 
L 541.146293 482.68965 
L 543.434566 478.003022 
L 545.769538 473.977161 
L 548.104511 470.62321 
L 550.392784 467.755479 
L 552.727756 465.065249 
L 555.016029 462.647294 
L 557.351002 460.561508 
L 559.639275 459.086389 
L 561.974247 458.248142 
L 564.26252 457.987281 
L 566.597493 458.084213 
L 568.885766 458.372053 
L 571.220738 458.86301 
L 573.509011 459.699243 
L 575.843984 461.108083 
L 578.132257 463.074685 
L 587.378748 472.674784 
L 589.71372 475.421073 
L 592.001993 478.990324 
L 594.336966 483.829959 
L 596.625239 489.603407 
L 601.248484 502.013454 
L 603.583457 507.470708 
L 605.87173 512.079652 
L 615.118221 528.852929 
L 617.453193 532.82559 
L 619.741466 536.3236 
L 624.364711 542.862032 
L 626.699684 546.42086 
L 631.322929 553.838982 
L 633.611202 557.16133 
L 638.234448 563.217138 
L 640.56942 566.85491 
L 642.904393 571.511745 
L 645.192666 577.11691 
L 652.150884 596.07699 
L 654.439157 601.420083 
L 656.774129 606.137327 
L 659.062402 610.095972 
L 661.397375 613.507063 
L 663.685648 616.247022 
L 666.02062 618.443963 
L 668.308893 620.056141 
L 670.643866 621.197647 
L 672.932139 621.816366 
L 675.267111 621.867778 
L 677.555384 621.286741 
L 679.890357 620.073629 
L 682.17863 618.383814 
L 684.513602 616.213511 
L 686.801875 613.504428 
L 689.136847 609.736724 
L 691.42512 604.586352 
L 693.760093 597.618208 
L 696.048366 589.415214 
L 700.671611 572.157531 
L 703.006584 564.835816 
L 705.294857 558.936938 
L 707.629829 553.835688 
L 714.541348 540.043684 
L 719.164593 530.837886 
L 721.499566 525.120882 
L 723.787839 517.709269 
L 726.122811 507.808743 
L 728.411084 496.177661 
L 730.746057 483.588308 
L 733.03433 472.121795 
L 735.369302 462.568178 
L 737.704275 455.546794 
L 739.992548 450.572234 
L 744.615793 442.318598 
L 746.950766 437.765798 
L 751.574011 428.313345 
L 760.820502 410.389754 
L 765.443747 400.052859 
L 767.73202 395.558039 
L 770.066993 392.296626 
L 772.355266 390.940043 
L 774.690238 391.699327 
L 776.978511 394.463048 
L 779.313484 398.956413 
L 781.601757 404.458075 
L 790.848248 428.809199 
L 793.18322 434.307583 
L 797.806466 444.371372 
L 800.094739 449.913707 
L 802.429711 457.148717 
L 804.717984 466.618942 
L 807.052957 478.920639 
L 811.676202 507.169583 
L 813.964475 519.609371 
L 816.299448 529.34895 
L 818.587721 535.960398 
L 823.210966 545.909363 
L 825.545939 552.720866 
L 827.834212 561.763149 
L 830.169184 572.952078 
L 832.504156 584.887636 
L 834.792429 595.985702 
L 837.127402 605.762115 
L 839.415675 613.517496 
L 841.750647 619.759599 
L 844.03892 624.665836 
L 846.373893 628.893781 
L 850.997138 636.236228 
L 855.620384 643.054752 
L 857.908657 646.092636 
L 860.243629 648.741691 
L 862.531902 650.772679 
L 864.866875 652.234688 
L 867.155148 653.104375 
L 869.49012 653.458349 
L 871.778393 653.282744 
L 874.113366 652.542385 
L 876.401639 651.262496 
L 878.736611 649.443547 
L 881.024884 647.244456 
L 883.359857 644.647288 
L 885.64813 641.789265 
L 890.271375 635.287639 
L 897.229593 625.018878 
L 899.517866 620.715538 
L 901.852839 614.681409 
L 904.141112 606.547178 
L 906.476084 595.979361 
L 911.099329 572.006208 
L 913.387602 561.293903 
L 915.722575 552.43196 
L 918.010848 545.804982 
L 920.34582 540.503782 
L 922.634093 535.833813 
L 924.969066 530.745044 
L 927.304038 524.712339 
L 929.592311 517.534797 
L 931.927284 508.805835 
L 934.215557 499.070929 
L 938.838802 478.622649 
L 941.173775 469.931355 
L 943.462048 463.451369 
L 945.79702 458.81987 
L 952.708539 448.475483 
L 957.331784 440.060881 
L 959.666757 436.45763 
L 961.95503 433.693009 
L 971.201521 424.417199 
L 973.536493 423.231414 
L 975.824766 423.424911 
L 978.159738 424.799578 
L 982.782984 428.381026 
L 985.071257 429.895811 
L 987.406229 431.779897 
L 989.694502 434.612255 
L 992.029475 438.677932 
L 994.317748 443.237998 
L 996.65272 447.524594 
L 998.940993 450.884764 
L 1001.275966 454.051808 
L 1003.564239 458.43063 
L 1005.899211 465.838709 
L 1008.187484 476.501842 
L 1012.81073 502.208132 
L 1015.145702 511.953657 
L 1017.480675 517.012011 
L 1017.480675 517.012011 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_52">
    <path d="M 94.886357 553.501166 
L 97.221329 549.293078 
L 99.509602 538.390247 
L 101.844574 545.163284 
L 106.46782 515.686408 
L 108.756093 503.815705 
L 111.091065 494.301669 
L 113.379338 483.571774 
L 115.714311 469.808149 
L 118.002584 463.971767 
L 120.337556 463.106732 
L 122.625829 470.457486 
L 124.960802 458.068928 
L 127.249075 466.463891 
L 129.584047 446.271558 
L 131.87232 461.200635 
L 134.207293 430.442149 
L 136.495566 425.896836 
L 138.830538 426.131359 
L 141.118811 425.048467 
L 143.453784 421.516292 
L 145.742057 417.516705 
L 148.077029 436.469891 
L 150.365302 447.522478 
L 152.700275 423.854817 
L 154.988548 447.990993 
L 157.32352 439.812663 
L 159.611793 425.964304 
L 161.946766 438.015467 
L 164.235039 438.709328 
L 166.570011 447.03242 
L 168.904983 461.538869 
L 171.193257 457.069471 
L 173.528229 463.313298 
L 175.816502 458.895238 
L 178.151474 459.249687 
L 180.439747 470.221357 
L 182.77472 478.07936 
L 185.062993 497.842768 
L 187.397965 491.279476 
L 189.686238 512.121288 
L 192.021211 524.053588 
L 194.309484 521.189475 
L 196.644456 530.711511 
L 198.932729 535.664261 
L 201.267702 537.292552 
L 203.555975 535.260532 
L 205.890947 553.659119 
L 208.17922 561.037812 
L 210.514193 567.668885 
L 212.802466 583.065727 
L 215.137438 593.375103 
L 217.425711 614.082506 
L 219.760684 613.518514 
L 222.048957 615.083622 
L 224.383929 608.197428 
L 226.672202 616.856518 
L 229.007175 611.161714 
L 231.295448 614.389978 
L 233.63042 618.009917 
L 235.918693 619.194315 
L 238.253666 625.01307 
L 240.541939 624.474081 
L 242.876911 659.98777 
L 245.165184 620.482799 
L 247.500156 648.595133 
L 249.788429 628.158673 
L 252.123402 618.389517 
L 254.411675 619.910306 
L 256.746647 616.659605 
L 259.03492 616.04049 
L 261.369893 614.029977 
L 263.704865 613.273547 
L 265.993138 607.382386 
L 268.328111 639.212734 
L 270.616384 603.679108 
L 272.951356 619.545888 
L 275.239629 601.367985 
L 277.574602 574.369631 
L 279.862875 572.029633 
L 282.197847 557.935719 
L 284.48612 552.851571 
L 286.821093 550.480213 
L 289.109366 543.878819 
L 291.444338 539.443303 
L 293.732611 537.541044 
L 296.067584 536.002539 
L 298.355857 533.861935 
L 300.690829 525.778892 
L 302.979102 500.184528 
L 305.314075 500.967844 
L 307.602348 488.6099 
L 309.93732 482.96681 
L 312.225593 474.399305 
L 314.560566 471.973816 
L 316.848839 498.06962 
L 319.183811 467.051818 
L 321.472084 481.017089 
L 323.807056 472.260421 
L 326.095329 449.399691 
L 328.430302 456.97015 
L 330.718575 455.056055 
L 333.053547 453.733439 
L 335.34182 466.56438 
L 337.676793 457.68302 
L 339.965066 462.062135 
L 342.300038 462.607494 
L 344.588311 450.495508 
L 346.923284 458.14492 
L 349.211557 476.733273 
L 351.546529 455.233962 
L 353.834802 469.091002 
L 356.169775 470.890723 
L 358.504747 479.481545 
L 360.79302 483.961137 
L 363.127993 501.888645 
L 365.416266 503.034168 
L 367.751238 524.222852 
L 370.039511 504.828691 
L 372.374484 515.212826 
L 374.662757 519.603183 
L 376.997729 520.687236 
L 379.286002 527.990429 
L 381.620975 529.959334 
L 383.909248 542.018009 
L 386.24422 574.060915 
L 388.532493 544.247228 
L 390.867465 569.83539 
L 393.155739 560.759556 
L 395.490711 550.76404 
L 397.778984 559.08182 
L 400.113956 566.230366 
L 402.402229 572.41175 
L 404.737202 586.63703 
L 407.025475 584.354689 
L 409.360447 591.199824 
L 411.64872 585.566404 
L 413.983693 578.377973 
L 416.271966 589.89772 
L 418.606938 582.15096 
L 420.895211 590.628243 
L 423.230184 587.106554 
L 425.518457 595.426574 
L 427.853429 584.276789 
L 430.141702 609.666976 
L 432.476675 596.893967 
L 434.764948 607.312442 
L 437.09992 590.441716 
L 439.388193 582.491387 
L 441.723166 583.621737 
L 444.011439 600.820225 
L 446.346411 589.996606 
L 448.634684 578.137107 
L 450.969657 591.577294 
L 453.304629 604.359244 
L 455.592902 594.183758 
L 457.927875 594.142476 
L 460.216148 589.056442 
L 462.55112 574.565635 
L 464.839393 559.212885 
L 467.174365 562.000959 
L 469.462638 555.23836 
L 471.797611 563.808164 
L 474.085884 544.495721 
L 476.420856 567.077824 
L 478.709129 537.990644 
L 481.044102 589.251409 
L 483.332375 539.595891 
L 485.667347 565.888174 
L 487.95562 544.376081 
L 490.290593 530.660283 
L 492.578866 534.483756 
L 494.913838 532.657174 
L 497.202111 536.50247 
L 499.537084 532.423474 
L 501.825357 531.46949 
L 504.160329 536.894285 
L 506.448602 553.965398 
L 508.783575 524.066644 
L 511.071848 547.176644 
L 513.40682 527.36983 
L 515.695093 510.669154 
L 518.030066 516.063072 
L 520.318339 511.081013 
L 522.653311 512.634286 
L 524.941584 510.444802 
L 527.276557 505.313067 
L 529.56483 530.979339 
L 531.899802 520.119342 
L 534.188075 510.765529 
L 536.523048 513.835541 
L 538.811321 500.82796 
L 541.146293 479.954927 
L 543.434566 483.329514 
L 545.769538 476.786693 
L 548.104511 478.91965 
L 550.392784 485.958117 
L 552.727756 474.47409 
L 555.016029 484.322 
L 557.351002 471.395167 
L 559.639275 475.225982 
L 561.974247 454.462108 
L 564.26252 472.223993 
L 566.597493 461.211561 
L 568.885766 462.498735 
L 571.220738 461.015617 
L 573.509011 471.003925 
L 575.843984 477.366955 
L 578.132257 489.742952 
L 580.467229 476.085995 
L 582.755502 498.558426 
L 585.090475 475.645204 
L 587.378748 473.74372 
L 589.71372 479.348908 
L 592.001993 479.238856 
L 594.336966 487.284164 
L 596.625239 492.398435 
L 598.960211 487.248486 
L 601.248484 537.662126 
L 603.583457 513.503272 
L 605.87173 530.259698 
L 608.206702 526.587344 
L 610.494975 520.563249 
L 612.829947 518.404094 
L 615.118221 521.824366 
L 617.453193 534.036823 
L 619.741466 538.471566 
L 622.076438 541.042177 
L 624.364711 545.525964 
L 626.699684 541.974282 
L 628.987957 539.39815 
L 631.322929 557.65285 
L 633.611202 538.822981 
L 635.946175 562.446337 
L 638.234448 544.443296 
L 640.56942 556.072085 
L 642.904393 558.474617 
L 645.192666 570.298218 
L 647.527638 564.710639 
L 649.815911 600.401832 
L 652.150884 572.118284 
L 654.439157 592.935611 
L 656.774129 581.973216 
L 659.062402 590.03489 
L 661.397375 591.280286 
L 663.685648 596.99748 
L 666.02062 594.073173 
L 668.308893 601.930048 
L 670.643866 622.695285 
L 672.932139 609.150768 
L 675.267111 619.435764 
L 677.555384 615.021368 
L 679.890357 595.421048 
L 682.17863 597.379722 
L 684.513602 593.368368 
L 686.801875 592.442559 
L 689.136847 591.836222 
L 691.42512 590.572599 
L 693.760093 576.476404 
L 696.048366 599.78547 
L 698.383338 575.677884 
L 700.671611 574.78553 
L 703.006584 569.919083 
L 705.294857 542.70162 
L 707.629829 546.32249 
L 709.918102 538.892911 
L 712.253075 536.464945 
L 714.541348 546.145351 
L 716.87632 532.57678 
L 719.164593 533.332947 
L 721.499566 529.917884 
L 723.787839 505.985729 
L 726.122811 505.264147 
L 728.411084 496.561959 
L 730.746057 481.223305 
L 733.03433 478.271173 
L 735.369302 469.602858 
L 737.704275 453.484442 
L 739.992548 496.843583 
L 742.32752 456.854717 
L 744.615793 472.360837 
L 746.950766 460.455253 
L 749.239039 438.126093 
L 751.574011 441.372338 
L 753.862284 440.556412 
L 756.197257 434.024131 
L 758.48553 441.7898 
L 760.820502 433.414292 
L 763.108775 428.997357 
L 765.443747 430.751269 
L 767.73202 413.503195 
L 770.066993 410.159002 
L 772.355266 416.472217 
L 774.690238 411.236732 
L 776.978511 413.288094 
L 779.313484 417.290054 
L 781.601757 432.578202 
L 783.936729 424.282371 
L 786.225002 455.784224 
L 788.559975 436.247862 
L 790.848248 466.94059 
L 793.18322 446.058783 
L 795.471493 445.875811 
L 797.806466 454.919462 
L 800.094739 454.615811 
L 802.429711 466.562975 
L 804.717984 472.647644 
L 807.052957 474.154342 
L 809.34123 526.156071 
L 811.676202 512.51177 
L 813.964475 538.595104 
L 816.299448 534.065173 
L 818.587721 534.453056 
L 820.922693 529.473718 
L 823.210966 537.001566 
L 827.834212 556.593772 
L 830.169184 564.55995 
L 832.504156 582.694715 
L 834.792429 573.920112 
L 837.127402 593.446614 
L 839.415675 598.53295 
L 841.750647 608.105136 
L 844.03892 605.187312 
L 846.373893 600.459023 
L 848.662166 609.842674 
L 850.997138 609.144583 
L 853.285411 615.897376 
L 855.620384 620.208 
L 857.908657 646.238126 
L 860.243629 642.642162 
L 862.531902 626.76548 
L 864.866875 645.325481 
L 867.155148 620.063896 
L 869.49012 625.588746 
L 871.778393 625.962724 
L 874.113366 623.401356 
L 876.401639 628.001529 
L 878.736611 628.228081 
L 881.024884 623.989029 
L 883.359857 634.085855 
L 885.64813 609.391424 
L 887.983102 626.42965 
L 890.271375 612.327263 
L 892.606348 601.379093 
L 894.894621 609.793104 
L 897.229593 599.647257 
L 899.517866 598.042149 
L 901.852839 597.337 
L 904.141112 592.218403 
L 906.476084 586.537163 
L 908.764357 577.356297 
L 911.099329 569.542584 
L 913.387602 559.093761 
L 915.722575 536.18399 
L 918.010848 538.156402 
L 920.34582 530.746839 
L 922.634093 531.132583 
L 924.969066 521.71856 
L 927.304038 518.354439 
L 929.592311 541.70103 
L 931.927284 515.81178 
L 934.215557 519.137994 
L 936.550529 503.246142 
L 938.838802 486.587041 
L 941.173775 471.679854 
L 943.462048 469.19328 
L 945.79702 463.424062 
L 948.085293 464.283001 
L 950.420266 457.594169 
L 952.708539 449.845595 
L 955.043511 486.700965 
L 957.331784 458.410271 
L 959.666757 467.298343 
L 961.95503 460.228117 
L 964.290002 443.312745 
L 966.578275 442.389607 
L 968.913248 439.071543 
L 971.201521 438.313908 
L 973.536493 449.149502 
L 975.824766 441.726002 
L 978.159738 443.271599 
L 980.448012 449.528843 
L 982.782984 429.57794 
L 985.071257 436.62375 
L 987.406229 456.997777 
L 989.694502 437.401203 
L 992.029475 453.912893 
L 994.317748 451.277932 
L 996.65272 477.606223 
L 998.940993 459.573404 
L 1001.275966 488.983044 
L 1003.564239 463.087907 
L 1005.899211 497.784073 
L 1008.187484 478.494743 
L 1010.522457 485.898001 
L 1012.81073 500.836756 
L 1015.145702 507.096583 
L 1017.480675 513.707224 
L 1017.480675 513.707224 
" clip-path="url(#p7664c140b1)" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
   </g>
   <g id="patch_8">
    <path d="M 48.756641 673.440156 
L 48.756641 377.487656 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 1063.610391 673.440156 
L 1063.610391 377.487656 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 48.756641 673.440156 
L 1063.610391 673.440156 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 48.756641 377.487656 
L 1063.610391 377.487656 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_31">
    <!-- 振动位移时间序列对比 -->
    <g transform="translate(493.183516 371.487656)scale(0.126 -0.126)">
     <defs>
      <path id="SimSun-5e8f" d="M 1000 4575 
L 1375 4350 
L 5050 4350 
L 5425 4725 
L 5950 4200 
L 1375 4200 
Q 1375 3225 1350 2462 
Q 1325 1700 1112 962 
Q 900 225 300 -575 
L 225 -525 
Q 725 350 862 1087 
Q 1000 1825 1012 2687 
Q 1025 3550 1000 4575 
z
M 3000 5225 
Q 3450 5075 3625 4937 
Q 3800 4800 3762 4637 
Q 3725 4475 3612 4400 
Q 3500 4325 3400 4600 
Q 3275 4875 2950 5150 
L 3000 5225 
z
M 2675 0 
Q 3100 -50 3325 -50 
Q 3550 -50 3575 225 
L 3575 2050 
L 2325 2050 
Q 2000 2050 1725 1975 
L 1500 2200 
L 3625 2200 
Q 3625 2400 3400 2637 
Q 3175 2875 2925 3075 
L 2975 3150 
Q 3325 3050 3775 2800 
L 4750 3550 
L 2725 3550 
Q 2400 3550 2125 3475 
L 1900 3700 
L 4775 3700 
L 5050 3950 
L 5475 3425 
Q 5200 3425 4837 3262 
Q 4475 3100 3875 2725 
Q 4000 2575 3975 2450 
Q 3950 2325 3825 2200 
L 5325 2200 
L 5550 2475 
L 6025 2000 
Q 5600 2000 5100 1350 
L 5000 1400 
L 5350 2050 
L 3900 2050 
L 3900 125 
Q 3900 -375 3400 -600 
Q 3325 -275 2675 -125 
L 2675 0 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5217" d="M 550 4550 
L 3175 4550 
L 3500 4875 
L 3975 4400 
L 2325 4400 
Q 2125 3800 1925 3325 
L 2925 3325 
L 3150 3575 
L 3575 3200 
L 3325 3050 
Q 3075 2075 2787 1525 
Q 2500 975 1975 462 
Q 1450 -50 425 -525 
L 375 -450 
Q 1475 200 2062 1000 
Q 2650 1800 2975 3175 
L 1875 3175 
Q 1750 2925 1600 2650 
Q 2250 2125 2212 1850 
Q 2175 1575 2075 1500 
L 1950 1425 
Q 1850 1425 1825 1725 
Q 1775 2100 1550 2550 
Q 1100 1925 550 1375 
L 475 1450 
Q 1050 2225 1375 2925 
Q 1700 3625 1875 4400 
L 1375 4400 
Q 1050 4400 775 4325 
L 550 4550 
z
M 4100 650 
Q 4125 1525 4125 2475 
Q 4125 3425 4100 4100 
L 4650 3875 
L 4450 3675 
L 4450 1625 
Q 4450 1200 4475 800 
L 4100 650 
z
M 5325 3950 
Q 5325 4850 5300 5150 
L 5875 4900 
L 5675 4700 
L 5675 225 
Q 5700 -400 5050 -600 
Q 5025 -175 4125 0 
L 4125 100 
Q 4825 50 5075 37 
Q 5325 25 5325 350 
L 5325 3950 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-5bf9" d="M 400 4250 
L 2375 4250 
L 2575 4500 
L 3000 4150 
L 2750 3950 
Q 2400 2850 2075 2100 
Q 2575 1375 2675 1062 
Q 2775 750 2762 537 
Q 2750 325 2662 237 
Q 2575 150 2550 150 
Q 2450 150 2375 575 
Q 2250 1075 1925 1800 
Q 1675 1350 1275 875 
Q 875 400 250 -100 
L 225 25 
Q 675 450 1087 1037 
Q 1500 1625 1725 2100 
Q 1000 3125 675 3550 
L 750 3625 
Q 1300 3025 1850 2400 
Q 2100 3050 2375 4100 
L 1225 4100 
Q 900 4100 625 4025 
L 400 4250 
z
M 3600 125 
Q 4375 0 4537 25 
Q 4700 50 4700 225 
L 4700 3350 
L 3525 3350 
Q 3200 3350 2925 3275 
L 2700 3500 
L 4700 3500 
L 4700 4350 
Q 4700 4800 4675 5125 
L 5275 4825 
L 5050 4625 
L 5050 3500 
L 5325 3500 
L 5650 3825 
L 6125 3350 
L 5050 3350 
L 5050 50 
Q 5050 -375 4500 -600 
Q 4500 -300 3600 0 
L 3600 125 
z
M 3100 2775 
L 3175 2825 
Q 3700 2400 3812 2175 
Q 3925 1950 3925 1800 
Q 3925 1625 3800 1462 
Q 3675 1300 3650 1300 
Q 3550 1300 3525 1650 
Q 3450 2125 3100 2775 
z
" transform="scale(0.015625)"/>
      <path id="SimSun-6bd4" d="M 1050 3400 
Q 1050 4525 1025 5075 
L 1625 4800 
L 1400 4600 
L 1400 2925 
L 2400 2925 
L 2700 3225 
L 3150 2775 
L 1400 2775 
L 1400 350 
L 3050 925 
L 3100 825 
Q 1625 150 1250 -250 
L 950 175 
Q 1050 275 1050 525 
L 1050 3400 
z
M 3425 3825 
Q 3425 4575 3400 5125 
L 4025 4825 
L 3775 4625 
L 3775 2525 
Q 4300 2925 4712 3400 
Q 5125 3875 5275 4175 
L 5775 3700 
Q 5475 3650 5025 3250 
Q 4550 2850 3775 2375 
L 3775 425 
Q 3775 125 4100 125 
L 5125 125 
Q 5325 125 5400 350 
Q 5475 575 5500 1425 
L 5625 1425 
Q 5625 825 5712 550 
Q 5800 275 6000 175 
Q 5875 -25 5712 -112 
Q 5550 -200 5275 -200 
L 3975 -200 
Q 3425 -200 3425 275 
L 3425 3825 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimSun-632f"/>
     <use xlink:href="#SimSun-52a8" x="100"/>
     <use xlink:href="#SimSun-4f4d" x="200"/>
     <use xlink:href="#SimSun-79fb" x="300"/>
     <use xlink:href="#SimSun-65f6" x="400"/>
     <use xlink:href="#SimSun-95f4" x="500"/>
     <use xlink:href="#SimSun-5e8f" x="600"/>
     <use xlink:href="#SimSun-5217" x="700"/>
     <use xlink:href="#SimSun-5bf9" x="800"/>
     <use xlink:href="#SimSun-6bd4" x="900"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="line2d_53">
     <path d="M 925.010391 391.629844 
L 935.510391 391.629844 
L 946.010391 391.629844 
" style="fill: none; stroke: #ff0000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_32">
     <!-- 真实振动位移 -->
     <g transform="translate(954.410391 395.304844)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-771f"/>
      <use xlink:href="#SimSun-5b9e" x="100"/>
      <use xlink:href="#SimSun-632f" x="200"/>
      <use xlink:href="#SimSun-52a8" x="300"/>
      <use xlink:href="#SimSun-4f4d" x="400"/>
      <use xlink:href="#SimSun-79fb" x="500"/>
     </g>
    </g>
    <g id="line2d_54">
     <path d="M 925.010391 406.518516 
L 935.510391 406.518516 
L 946.010391 406.518516 
" style="fill: none; stroke-dasharray: 7.4,3.2; stroke-dashoffset: 0; stroke: #0000ff; stroke-width: 2"/>
    </g>
    <g id="text_33">
     <!-- LSTM+卡尔曼滤波预测 -->
     <g transform="translate(954.410391 410.193516)scale(0.105 -0.105)">
      <use xlink:href="#SimSun-4c"/>
      <use xlink:href="#SimSun-53" x="50"/>
      <use xlink:href="#SimSun-54" x="100"/>
      <use xlink:href="#SimSun-4d" x="150"/>
      <use xlink:href="#SimSun-2b" x="200"/>
      <use xlink:href="#SimSun-5361" x="250"/>
      <use xlink:href="#SimSun-5c14" x="350"/>
      <use xlink:href="#SimSun-66fc" x="450"/>
      <use xlink:href="#SimSun-6ee4" x="550"/>
      <use xlink:href="#SimSun-6ce2" x="650"/>
      <use xlink:href="#SimSun-9884" x="750"/>
      <use xlink:href="#SimSun-6d4b" x="850"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p5040f8f6e3">
   <rect x="48.756641" y="23.191406" width="1014.85375" height="295.9525"/>
  </clipPath>
  <clipPath id="p7664c140b1">
   <rect x="48.756641" y="377.487656" width="1014.85375" height="295.9525"/>
  </clipPath>
 </defs>
</svg>
