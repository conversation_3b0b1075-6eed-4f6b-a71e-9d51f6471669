#!/usr/bin/env python
# -*- coding: utf-8 -*-
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
file_name = r'E:\AFM课题组\LSTM\LSTM-KF-main\LSTM-KF-main\lstm_kF\2D\flight_tt.xls'
flight_data  = pd.read_excel(file_name)
flight_data.shape #(144, 3)

fig_size = plt.rcParams["figure.figsize"]
fig_size[0] = 15
fig_size[1] = 5
plt.rcParams["figure.figsize"] = fig_size

x_data = flight_data['x'].values.astype(float)
y_data = flight_data['y'].values.astype(float)

all_data = np.zeros((len(x_data),2))
all_data[:,0] = x_data
all_data[:,1] = y_data

test_data_size = 12
train_data = all_data[:-test_data_size]
test_data = all_data[-test_data_size:]

scaler = MinMaxScaler(feature_range=(-1, 1))
train_data_normalized = scaler.fit_transform(train_data .reshape(-1, 2))

train_data_normalized = torch.FloatTensor(train_data_normalized)

def create_inout_sequences(input_data, tw):
    inout_seq = []
    L = len(input_data)
    for i in range(L-tw):
        train_seq = input_data[i:i+tw]
        train_label = input_data[i+tw:i+tw+1]
        inout_seq.append((train_seq ,train_label))
    return inout_seq

train_window = 20
train_inout_seq = create_inout_sequences(train_data_normalized, train_window)

class LSTM(nn.Module):
    def __init__(self, input_size=2, hidden_size=100, output_size=2):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size,num_layers = 1)
        self.linear = nn.Linear(hidden_size, output_size)

    def forward(self, input_seq):
        lstm_out, self.hidden_cell = self.lstm(input_seq.view(len(input_seq) ,1, -1), self.hidden_cell)
        predictions = self.linear(lstm_out.view(len(input_seq), -1))
        return predictions[-1]

model_path = r'LSTM-KF-main/LSTM-KF-main/lstm_kF/2D/lstm_model_total.pt'
model = torch.load(model_path)

fut_pred = 1
test_inputs = train_data_normalized[-train_window:].tolist()

model.eval()

font = {'family': 'SimSun',  # 宋体
        'size': '10.5'  # 五号
        }
plt.rc('font', **font)
plt.rc('axes', unicode_minus=False)

class Kf_Params:
    B = 0  # 外部输入为0
    u = 0  # 外部输入为0
    K = float('nan')  # 卡尔曼增益无需初始化
    z = float('nan')  # 这里无需初始化，每次使用kf_update之前需要输入观察值z
    P = np.diag(np.ones(4))  # 初始P设为0 ??? zeros(4, 4)
    x = []
    G = []
    A = np.eye(4) + np.diag(np.ones((1, 2))[0, :], 2)
    Q = np.diag(np.ones(4)) * 0.1
    H = np.eye(2, 4)
    R = np.diag(np.ones(2)) * 0.1

def kf_init(px, py, vx, vy):
    kf_params = Kf_Params()
    kf_params.B = 0
    kf_params.u = 0
    kf_params.K = float('nan')
    kf_params.z = float('nan')
    kf_params.P = np.diag(np.ones(4))
    kf_params.x = [px, py, vx, vy]
    kf_params.G = [px, py, vx, vy]
    kf_params.A = np.eye(4) + np.diag(np.ones((1, 2))[0, :], 2)
    kf_params.Q = np.diag(np.ones(4)) * 0.1
    kf_params.H = np.eye(2, 4)
    kf_params.R = np.diag(np.ones(2)) * 0.1
    return kf_params

def kf_update(kf_params,t):
    a1 = np.dot(kf_params.A, kf_params.x)
    a2 = kf_params.B * kf_params.u
    x_ = np.array(a1) + np.array(a2)
    
    for i in range(fut_pred):
        seq = torch.FloatTensor(test_inputs[-train_window:])
        with torch.no_grad():
            model.hidden = (torch.zeros(1, 1, model.hidden_size),
                            torch.zeros(1, 1, model.hidden_size))
            list_model_seq = [model(seq)[0].item(),model(seq)[1].item()]
            test_inputs.append(list_model_seq)
    actual_predictions = scaler.inverse_transform(np.array(test_inputs[train_window:] ).reshape(-1, 2))
    print(actual_predictions)
    print(t)

    x_[0] = actual_predictions[t-1][0]
    x_[1] = actual_predictions[t-1][1]
    print(x_)

    b1 = np.dot(kf_params.A, kf_params.P)
    b2 = np.dot(b1, np.transpose(kf_params.A))
    p_ = np.array(b2) + np.array(kf_params.Q)
 
    c1 = np.dot(p_, np.transpose(kf_params.H))
    c2 = np.dot(kf_params.H, p_)
    c3 = np.dot(c2, np.transpose(kf_params.H))
    c4 = np.array(c3) + np.array(kf_params.R)
    c5 = np.linalg.matrix_power(c4, -1)
    kf_params.K = np.dot(c1, c5)
 
    d1 = np.dot(kf_params.H, x_)
    d2 = np.array(kf_params.z) - np.array(d1)
    d3 = np.dot(kf_params.K, d2)
    kf_params.x = np.array(x_) + np.array(d3)
 
    e1 = np.dot(kf_params.K, kf_params.H)
    e2 = np.dot(e1, p_)
    kf_params.P = np.array(p_) - np.array(e2)
 
    kf_params.G = x_
    return kf_params

def accuracy(predictions, labels):
    return np.array(predictions) - np.array(labels)

def mse(predictions, labels):
    return np.mean((np.array(predictions) - np.array(labels)) ** 2)

def mae(predictions, labels):
    return np.mean(np.abs(np.array(predictions) - np.array(labels)))

def rmse(predictions, labels):
    return np.sqrt(mse(predictions, labels))



if __name__ == '__main__':
    plt.figure()

    # 1. 读取数据
    path = r'LSTM-KF-main/LSTM-KF-main/lstm_kF/2D/true.csv'
    data_B = pd.read_csv(path)
    data_B_x = data_B['x'].values
    data_B_y = data_B['y'].values

    # 2. 初始化 KF
    kf_params = kf_init(data_B_x[0], data_B_y[0], 0, 0)

    predictions = []
    ground_truth = []

    # 3. Kalman 预测
    for t in range(1, len(data_B_x)):
        kf_params.z = [data_B_x[t], data_B_y[t]]
        kf_params = kf_update(kf_params, t)
        predictions.append(kf_params.x[:2])
        ground_truth.append([data_B_x[t], data_B_y[t]])

    # 4. 误差计算
    predictions = np.array(predictions)
    ground_truth = np.array(ground_truth)
    errors = predictions - ground_truth  # 误差

    # ✅ 5. 插入误差图绘制
    plt.figure(figsize=(12, 6))
    plt.plot(errors[:, 0], label='X方向误差')
    plt.plot(errors[:, 1], label='Y方向误差')
    plt.title('LSTM + KF 预测误差图')
    plt.xlabel('时间步')
    plt.ylabel('误差值')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()

 