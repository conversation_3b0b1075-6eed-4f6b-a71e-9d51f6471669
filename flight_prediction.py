"""
使用LSTM+卡尔曼滤波预测flight_tt.xls中的x坐标
基于displacement_real_time_demo.py修改
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import time
from collections import deque
import warnings
import random

def set_all_seeds(seed=42):
    """设置所有随机种子以确保结果可重复"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 设置随机种子
set_all_seeds(42)

# 忽略警告
warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    USE_CHINESE = True
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    USE_CHINESE = False
    print("使用默认字体")

# LSTM模型
class FlightLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

# 卡尔曼滤波器
class FlightKalmanFilter:
    def __init__(self, initial_process_variance=1e-4, initial_measurement_variance=5e-4):
        self.Q = initial_process_variance  # 过程噪声方差
        self.R = initial_measurement_variance  # 测量噪声方差
        self.x_hat = 0.0  # 状态估计值
        self.P = 1.0      # 估计误差协方差

        # 自适应参数
        self.measurement_history = []
        self.innovation_history = []
        self.max_history = 15

        self.adaptation_rate = 0.08
        
    def predict(self):
        """预测步骤"""
        self.P = self.P + self.Q
        
    def update(self, measurement):
        """更新步骤"""
        # 记录测量历史
        self.measurement_history.append(measurement)
        if len(self.measurement_history) > self.max_history:
            self.measurement_history.pop(0)
        
        # 计算创新
        innovation = measurement - self.x_hat
        self.innovation_history.append(innovation)
        if len(self.innovation_history) > self.max_history:
            self.innovation_history.pop(0)
        
        # 自适应调整
        self._adaptive_adjustment()
        
        # 卡尔曼增益
        K = self.P / (self.P + self.R)
        
        # 状态更新
        self.x_hat = self.x_hat + K * innovation
        self.P = (1 - K) * self.P
        
        return self.x_hat
    
    def _adaptive_adjustment(self):
        """自适应调整"""
        if len(self.measurement_history) < 3:
            return

        recent_measurements = np.array(self.measurement_history[-3:])
        changes = np.abs(np.diff(recent_measurements))
        avg_change = np.mean(changes)

        # 调整过程噪声
        if avg_change > 0.01:
            self.Q = min(1e-2, self.Q * 1.1)
        else:
            self.Q = max(1e-6, self.Q * 0.99)

        # 基于创新序列调整测量噪声
        if len(self.innovation_history) >= 3:
            innovation_var = np.var(self.innovation_history[-3:])
            if innovation_var > 0:
                target_R = innovation_var * 0.6
                self.R = (1 - self.adaptation_rate) * self.R + self.adaptation_rate * target_R
                self.R = np.clip(self.R, 1e-6, 1e-2)

# 实时预测器
class FlightRealTimePredictor:
    def __init__(self, model, scaler, window_size=25, use_kalman=True):
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.use_kalman = use_kalman
        self.model.eval()

        # 数据缓冲区
        self.data_buffer = deque(maxlen=window_size)

        # 卡尔曼滤波器
        if self.use_kalman:
            self.kalman_filter = FlightKalmanFilter()
        
        # 性能统计
        self.prediction_times = []
        self.predictions_made = 0
        
    def initialize_buffer(self, initial_data):
        """初始化数据缓冲区"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")
        
        # 标准化初始数据
        normalized_data = self.scaler.transform(
            np.array(initial_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()
        
        # 填充缓冲区
        self.data_buffer.clear()
        for value in normalized_data:
            self.data_buffer.append(value)
        
        print(f"预测器缓冲区已初始化，包含 {len(self.data_buffer)} 个数据点")
    
    def predict_next(self, new_data_point=None):
        """预测下一个值"""
        start_time = time.time()
        
        # 如果提供了新数据点，添加到缓冲区
        if new_data_point is not None:
            normalized_point = self.scaler.transform([[new_data_point]])[0, 0]
            self.data_buffer.append(normalized_point)
        
        # 检查缓冲区数据
        if len(self.data_buffer) < self.window_size:
            raise ValueError(f"缓冲区数据不足，需要 {self.window_size} 个点")
        
        # 准备输入序列
        input_seq = torch.FloatTensor(list(self.data_buffer)).unsqueeze(0).unsqueeze(-1)
        
        # LSTM预测
        with torch.no_grad():
            normalized_prediction = self.model(input_seq).item()
        
        # 反标准化
        lstm_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
        
        # 卡尔曼滤波优化
        if self.use_kalman:
            self.kalman_filter.predict()
            kalman_prediction = self.kalman_filter.update(lstm_prediction)
        else:
            kalman_prediction = lstm_prediction
        
        # 记录性能
        prediction_time = time.time() - start_time
        self.prediction_times.append(prediction_time)
        self.predictions_made += 1
        
        return lstm_prediction, kalman_prediction, prediction_time

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def read_excel_file(filename):
    """读取Excel文件"""
    try:
        # 尝试使用xlrd读取
        import xlrd
        workbook = xlrd.open_workbook(filename)
        sheet = workbook.sheet_by_index(0)
        
        # 假设x坐标在第一列（索引0）
        x_data = []
        for row in range(1, sheet.nrows):  # 跳过标题行
            try:
                value = sheet.cell_value(row, 0)
                if isinstance(value, (int, float)):
                    x_data.append(float(value))
            except:
                continue
        
        return np.array(x_data)
    
    except ImportError:
        print("xlrd包未安装，尝试其他方法...")
        # 如果没有xlrd，尝试使用openpyxl
        try:
            from openpyxl import load_workbook
            wb = load_workbook(filename)
            ws = wb.active
            
            x_data = []
            for row in ws.iter_rows(min_row=2, values_only=True):  # 跳过标题行
                if row[0] is not None and isinstance(row[0], (int, float)):
                    x_data.append(float(row[0]))
            
            return np.array(x_data)
        
        except ImportError:
            print("openpyxl包也未安装，请手动提供数据或安装相应的包")
            return None

def flight_prediction_demo():
    """flight_tt.xls x坐标预测演示"""
    print("=== flight_tt.xls x坐标LSTM+卡尔曼滤波预测演示 ===\n")
    
    # 1. 读取Excel文件
    print("正在读取flight_tt.xls文件...")
    x_data = read_excel_file('flight_tt.xls')
    
    if x_data is None:
        print("无法读取Excel文件，请检查文件是否存在或安装相应的包")
        return
    
    if len(x_data) == 0:
        print("未能从Excel文件中读取到有效数据")
        return
    
    print(f"成功读取x坐标数据:")
    print(f"  数据点数: {len(x_data)}")
    print(f"  x坐标范围: {np.min(x_data):.4f} ~ {np.max(x_data):.4f}")
    print(f"  x坐标标准差: {np.std(x_data):.4f}")
    print(f"  前10个数据点: {x_data[:10]}")
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    x_normalized = scaler.fit_transform(x_data.reshape(-1, 1))
    x_normalized = torch.FloatTensor(x_normalized.flatten())
    
    # 3. 训练模型
    window_size = 25
    X, y = create_sequences(x_normalized.numpy(), window_size)

    # 转换为PyTorch张量
    X = torch.FloatTensor(X).unsqueeze(-1)  # (batch, seq, feature)
    y = torch.FloatTensor(y)

    # 初始化模型
    model = FlightLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)

    print(f"\n开始训练x坐标预测模型...")
    print(f"模型参数: 窗口大小={window_size}, 隐藏层大小=96")

    # 训练模型
    epochs = 120
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 30 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    print("x坐标预测模型训练完成！")
    
    # 4. 实时预测演示
    split_point = int(len(x_data) * 0.8)
    historical_data = x_data[:split_point]
    streaming_data = x_data[split_point:]
    
    print(f"\n=== 实时预测设置 ===")
    print(f"历史数据长度: {len(historical_data)} 点")
    print(f"实时流数据长度: {len(streaming_data)} 点")
    print(f"数据分割点: 80%")
    
    # 创建实时预测器
    predictor = FlightRealTimePredictor(model, scaler, window_size)
    predictor.initialize_buffer(historical_data)
    
    # 开始实时预测
    print(f"\n开始x坐标实时预测...")

    real_values = []
    lstm_predictions = []
    kalman_predictions = []
    prediction_times = []

    for i, true_value in enumerate(streaming_data):
        # 预测下一个x坐标值
        lstm_pred, kalman_pred, pred_time = predictor.predict_next()

        # 记录结果
        real_values.append(true_value)
        lstm_predictions.append(lstm_pred)
        kalman_predictions.append(kalman_pred)
        prediction_times.append(pred_time)

        # 将真实观测值添加到预测器
        if i < len(streaming_data) - 1:
            predictor.predict_next(true_value)

        # 显示进度
        if (i + 1) % 20 == 0:
            print(f"已处理 {i + 1}/{len(streaming_data)} 个x坐标点，"
                  f"平均预测时间: {np.mean(prediction_times[-20:]):.4f}秒")
    
    return real_values, lstm_predictions, kalman_predictions, predictor

if __name__ == "__main__":
    results = flight_prediction_demo()
    
    if results is not None:
        real_values, lstm_predictions, kalman_predictions, predictor = results
        
        # 准确性分析
        lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
        lstm_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(lstm_predictions)) ** 2))

        kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))
        kalman_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(kalman_predictions)) ** 2))

        print(f"\n=== x坐标预测准确性对比 ===")
        print(f"LSTM原始预测:")
        print(f"  平均绝对误差 (MAE): {lstm_mae:.6f}")
        print(f"  均方根误差 (RMSE): {lstm_rmse:.6f}")
        print(f"卡尔曼滤波优化:")
        print(f"  平均绝对误差 (MAE): {kalman_mae:.6f}")
        print(f"  均方根误差 (RMSE): {kalman_rmse:.6f}")
        
        mae_improvement = ((lstm_mae - kalman_mae) / lstm_mae) * 100
        rmse_improvement = ((lstm_rmse - kalman_rmse) / lstm_rmse) * 100
        print(f"卡尔曼滤波改善:")
        print(f"  MAE改善: {mae_improvement:.2f}%")
        print(f"  RMSE改善: {rmse_improvement:.2f}%")

        # 可视化结果
        plt.figure(figsize=(15, 10))
        
        time_steps = np.arange(len(real_values))
        
        # 子图1: x坐标预测结果对比
        plt.subplot(2, 1, 1)
        plt.plot(time_steps, real_values, 'b-', label='真实x坐标', alpha=0.8, linewidth=2)
        plt.plot(time_steps, lstm_predictions, 'r--', label='LSTM预测', alpha=0.7, linewidth=1.5)
        plt.plot(time_steps, kalman_predictions, 'g-', label='卡尔曼优化', alpha=0.9, linewidth=2)

        plt.title('flight_tt.xls x坐标实时预测结果对比', fontsize=14, fontweight='bold')
        plt.xlabel('时间步')
        plt.ylabel('x坐标')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 添加统计信息
        info_text = f'LSTM MAE: {lstm_mae:.6f}\nKalman MAE: {kalman_mae:.6f}'
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
                 fontsize=10, verticalalignment='top',
                 bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # 子图2: 预测误差分析
        plt.subplot(2, 1, 2)
        lstm_errors = np.abs(np.array(real_values) - np.array(lstm_predictions))
        kalman_errors = np.abs(np.array(real_values) - np.array(kalman_predictions))

        plt.plot(time_steps, lstm_errors, 'r-', alpha=0.7, label='LSTM误差', linewidth=1.5)
        plt.plot(time_steps, kalman_errors, 'g-', alpha=0.9, label='卡尔曼误差', linewidth=2)

        plt.title('x坐标预测误差对比', fontsize=12)
        plt.xlabel('时间步')
        plt.ylabel('绝对误差')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('flight_x_coordinate_prediction.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"\n预测结果图像已保存到: flight_x_coordinate_prediction.png")
