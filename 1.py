#!/usr/bin/env python
# -*- coding: utf-8 -*-
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler

file_name = r'E:\AFM课题组\算法\LSTM\LSTM-KF-main\LSTM-KF-main\lstm_kF\2D\flight_tt.xls'
flight_data = pd.read_excel(file_name)
flight_data.shape #(144, 3)

fig_size = plt.rcParams["figure.figsize"]
fig_size[0] = 15
fig_size[1] = 5
plt.rcParams["figure.figsize"] = fig_size

x_data = flight_data['x'].values.astype(float)
y_data = flight_data['y'].values.astype(float)

all_data = np.zeros((len(x_data),2))
all_data[:,0] = x_data
all_data[:,1] = y_data

test_data_size = 12
train_data = all_data[:-test_data_size]
test_data = all_data[-test_data_size:]

scaler = MinMaxScaler(feature_range=(-1, 1))
train_data_normalized = scaler.fit_transform(train_data.reshape(-1, 2))
train_data_normalized = torch.FloatTensor(train_data_normalized)

def create_inout_sequences(input_data, tw):
    inout_seq = []
    L = len(input_data)
    for i in range(L-tw):
        train_seq = input_data[i:i+tw]
        train_label = input_data[i+tw:i+tw+1]
        inout_seq.append((train_seq, train_label))
    return inout_seq

train_window = 20
train_inout_seq = create_inout_sequences(train_data_normalized, train_window)

class LSTM(nn.Module):
    def __init__(self, input_size=2, hidden_size=100, output_size=2):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers=1)
        self.linear = nn.Linear(hidden_size, output_size)

    def forward(self, input_seq):
        lstm_out, self.hidden_cell = self.lstm(input_seq.view(len(input_seq), 1, -1), self.hidden_cell)
        predictions = self.linear(lstm_out.view(len(input_seq), -1))
        return predictions[-1]

def accuracy(predictions, labels):
    return np.array(predictions) - np.array(labels)

def mse(predictions, labels):
    return np.mean((np.array(predictions) - np.array(labels)) ** 2)

def mae(predictions, labels):
    return np.mean(np.abs(np.array(predictions) - np.array(labels)))

def rmse(predictions, labels):
    return np.sqrt(mse(predictions, labels))

if __name__ == '__main__':
    # 加载模型
    model_path = r'LSTM-KF-main/LSTM-KF-main/lstm_kF/2D/lstm_model_total.pt'
    model = torch.load(model_path)
    model.eval()

    # 准备测试数据
    test_inputs = train_data_normalized[-train_window:].tolist()
    predictions = []
    
    # 进行预测
    for i in range(test_data_size):
        seq = torch.FloatTensor(test_inputs[-train_window:])
        with torch.no_grad():
            model.hidden = (torch.zeros(1, 1, model.hidden_size),
                          torch.zeros(1, 1, model.hidden_size))
            pred = model(seq)
            test_inputs.append([pred[0].item(), pred[1].item()])
            predictions.append([pred[0].item(), pred[1].item()])

    # 反归一化预测结果
    predictions = scaler.inverse_transform(np.array(predictions).reshape(-1, 2))
    
    # 读取真实数据
    path = r'LSTM-KF-main/LSTM-KF-main/lstm_kF/2D/true.csv'
    data_B = pd.read_csv(path)
    ground_truth = np.array(list(zip(data_B.iloc[:, 0], data_B.iloc[:, 1])))
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 12))
    x_tick = np.arange(0, test_data_size, 1)

    # 第一个子图：x值预测图
    ax1.plot(x_tick, ground_truth[:, 0], color="red", label='True x value')
    ax1.plot(x_tick, predictions[:, 0], color="blue", linestyle='--', label='LSTM predicted x value')
    
    ax1.legend(loc="best", frameon=False)
    ax1.set_title('LSTM Prediction Results for x value')
    ax1.set_xlabel('Time Step')
    ax1.set_ylabel('x value')
    ax1.grid(True)

    # 计算x值误差
    x_errors = predictions[:, 0] - ground_truth[:, 0]

    # 第二个子图：x值误差图
    ax2.plot(x_tick, x_errors, label='X direction error', color='blue')
    ax2.set_title('LSTM Prediction Error for x value')
    ax2.set_xlabel('Time Step')
    ax2.set_ylabel('Error Value')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig('lstm_prediction_x_result.svg', dpi=600)
    plt.show()

    # 计算x值的评估指标
    print("\nEvaluation Metrics for x value:")
    print("Mean Squared Error (MSE):", mse(predictions[:, 0], ground_truth[:, 0]))
    print("Mean Absolute Error (MAE):", mae(predictions[:, 0], ground_truth[:, 0]))
    print("Root Mean Squared Error (RMSE):", rmse(predictions[:, 0], ground_truth[:, 0]))
